<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 6: 交互控制</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
        }
        
        .controls {
            width: 380px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .interaction-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .interaction-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .status-display {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .instructions h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .instructions p {
            color: #856404;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .key {
            background: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🎮 交互控制</h1>
        <p>学习鼠标控制、键盘输入和物体拾取</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <div class="instructions">
                <h4>🎮 操作说明</h4>
                <p><span class="key">鼠标左键</span> - 选择物体</p>
                <p><span class="key">鼠标拖拽</span> - 旋转相机</p>
                <p><span class="key">滚轮</span> - 缩放视图</p>
                <p><span class="key">WASD</span> - 移动选中物体</p>
                <p><span class="key">QE</span> - 上下移动物体</p>
            </div>
            
            <h3>🎯 交互控制</h3>
            
            <div class="interaction-section">
                <h4>📍 选中物体信息</h4>
                <div class="status-display" id="selectedObject">
                    未选中任何物体
                </div>
            </div>
            
            <div class="interaction-section">
                <h4>🖱️ 鼠标状态</h4>
                <div class="status-display" id="mouseStatus">
                    位置: (0, 0)
                </div>
            </div>
            
            <div class="interaction-section">
                <h4>⌨️ 键盘状态</h4>
                <div class="status-display" id="keyboardStatus">
                    无按键按下
                </div>
            </div>
            
            <div class="control-group">
                <label for="moveSpeed">移动速度</label>
                <input type="range" id="moveSpeed" min="0.01" max="0.1" step="0.01" value="0.05">
                <div class="value-display" id="moveSpeed-value">0.05</div>
            </div>
            
            <div class="control-group">
                <button id="addCube">➕ 添加立方体</button>
                <button id="addSphere">➕ 添加球体</button>
                <button id="deleteObject">🗑️ 删除选中物体</button>
                <button id="resetScene">🔄 重置场景</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>射线投射</strong>：用于检测鼠标点击的物体</p>
                <p><strong>事件监听</strong>：处理鼠标和键盘输入</p>
                <p><strong>相机控制</strong>：实现交互式视角控制</p>
                <p><strong>物体操作</strong>：动态添加、删除和移动物体</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="interaction-controls.js"></script>
</body>
</html>
