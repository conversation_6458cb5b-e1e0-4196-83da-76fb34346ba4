/**
 * Three.js Demo 6: 交互控制
 * 学习鼠标控制、键盘输入和物体拾取
 *
 * 重点学习：
 * 1. 射线投射(Raycasting)进行物体拾取
 * 2. 鼠标事件处理
 * 3. 键盘输入控制
 * 4. 相机交互控制
 */

// 全局变量
let scene, camera, renderer;
let raycaster, mouse;
let objects = []; // 场景中的所有可交互物体
let selectedObject = null; // 当前选中的物体
let animationId;

// 控制参数
let moveSpeed = 0.05;
let isMouseDown = false;
let previousMousePosition = { x: 0, y: 0 };

// 键盘状态
let keys = {
  w: false,
  a: false,
  s: false,
  d: false,
  q: false,
  e: false,
};

/**
 * 初始化场景
 */
function init() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(0, 5, 10);
  camera.lookAt(0, 0, 0);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();
  renderer.setSize(containerRect.width, containerRect.height);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  container.appendChild(renderer.domElement);

  // 创建射线投射器和鼠标向量
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();

  // 添加光源
  addLights();

  // 创建地面
  createGround();

  // 创建初始物体
  createInitialObjects();

  // 设置事件监听
  setupEventListeners();

  // 设置控制器
  setupControls();

  // 开始动画循环
  animate();
}

/**
 * 添加光源
 */
function addLights() {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  // 方向光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 1024;
  directionalLight.shadow.mapSize.height = 1024;
  scene.add(directionalLight);
}

/**
 * 创建地面
 */
function createGround() {
  const geometry = new THREE.PlaneGeometry(20, 20);
  const material = new THREE.MeshLambertMaterial({ color: 0x808080 });
  const ground = new THREE.Mesh(geometry, material);
  ground.rotation.x = -Math.PI / 2;
  ground.receiveShadow = true;
  scene.add(ground);
}

/**
 * 创建初始物体
 */
function createInitialObjects() {
  // 添加几个立方体和球体
  addCube(-2, 1, 0, 0xff6b6b);
  addSphere(2, 1, 0, 0x4ecdc4);
  addCube(0, 1, -2, 0x45b7d1);
}

/**
 * 添加立方体
 */
function addCube(x = 0, y = 1, z = 0, color = 0x3498db) {
  const geometry = new THREE.BoxGeometry(1, 1, 1);
  const material = new THREE.MeshStandardMaterial({ color: color });
  const cube = new THREE.Mesh(geometry, material);

  cube.position.set(x, y, z);
  cube.castShadow = true;
  cube.userData = { type: "cube", originalColor: color };

  scene.add(cube);
  objects.push(cube);
}

/**
 * 添加球体
 */
function addSphere(x = 0, y = 1, z = 0, color = 0xe74c3c) {
  const geometry = new THREE.SphereGeometry(0.5, 32, 32);
  const material = new THREE.MeshStandardMaterial({ color: color });
  const sphere = new THREE.Mesh(geometry, material);

  sphere.position.set(x, y, z);
  sphere.castShadow = true;
  sphere.userData = { type: "sphere", originalColor: color };

  scene.add(sphere);
  objects.push(sphere);
}

/**
 * 设置事件监听
 */
function setupEventListeners() {
  const canvas = renderer.domElement;

  // 鼠标事件
  canvas.addEventListener("click", onMouseClick);
  canvas.addEventListener("mousedown", onMouseDown);
  canvas.addEventListener("mousemove", onMouseMove);
  canvas.addEventListener("mouseup", onMouseUp);
  canvas.addEventListener("wheel", onMouseWheel);

  // 键盘事件
  document.addEventListener("keydown", onKeyDown);
  document.addEventListener("keyup", onKeyUp);

  // 防止右键菜单
  canvas.addEventListener("contextmenu", (event) => {
    event.preventDefault();
  });
}

/**
 * 鼠标点击事件 - 物体拾取
 */
function onMouseClick(event) {
  const rect = renderer.domElement.getBoundingClientRect();

  // 将鼠标位置转换为标准化设备坐标 (-1 到 +1)
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 更新射线投射器
  raycaster.setFromCamera(mouse, camera);

  // 检测与物体的交集
  const intersects = raycaster.intersectObjects(objects);

  if (intersects.length > 0) {
    // 选中第一个相交的物体
    selectObject(intersects[0].object);
  } else {
    // 取消选择
    selectObject(null);
  }

  updateMouseStatus(event);
}

/**
 * 鼠标按下事件
 */
function onMouseDown(event) {
  isMouseDown = true;
  previousMousePosition = { x: event.clientX, y: event.clientY };
}

/**
 * 鼠标移动事件 - 相机控制
 */
function onMouseMove(event) {
  if (isMouseDown) {
    const deltaX = event.clientX - previousMousePosition.x;
    const deltaY = event.clientY - previousMousePosition.y;

    // 旋转相机
    const spherical = new THREE.Spherical();
    spherical.setFromVector3(camera.position);

    spherical.theta -= deltaX * 0.01;
    spherical.phi += deltaY * 0.01;

    // 限制垂直角度
    spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));

    camera.position.setFromSpherical(spherical);
    camera.lookAt(0, 0, 0);

    previousMousePosition = { x: event.clientX, y: event.clientY };
  }

  updateMouseStatus(event);
}

/**
 * 鼠标抬起事件
 */
function onMouseUp(event) {
  isMouseDown = false;
}

/**
 * 鼠标滚轮事件 - 缩放
 */
function onMouseWheel(event) {
  const scale = event.deltaY > 0 ? 1.1 : 0.9;
  camera.position.multiplyScalar(scale);

  // 限制缩放范围
  const distance = camera.position.length();
  if (distance < 3) {
    camera.position.normalize().multiplyScalar(3);
  } else if (distance > 20) {
    camera.position.normalize().multiplyScalar(20);
  }

  event.preventDefault();
}

/**
 * 键盘按下事件
 */
function onKeyDown(event) {
  const key = event.key.toLowerCase();
  if (keys.hasOwnProperty(key)) {
    keys[key] = true;
    updateKeyboardStatus();
  }
}

/**
 * 键盘抬起事件
 */
function onKeyUp(event) {
  const key = event.key.toLowerCase();
  if (keys.hasOwnProperty(key)) {
    keys[key] = false;
    updateKeyboardStatus();
  }
}

/**
 * 选择物体
 */
function selectObject(object) {
  // 恢复之前选中物体的颜色
  if (selectedObject) {
    selectedObject.material.color.setHex(selectedObject.userData.originalColor);
  }

  selectedObject = object;

  if (selectedObject) {
    // 高亮选中的物体
    selectedObject.material.color.setHex(0xffff00); // 黄色高亮
  }

  updateSelectedObjectDisplay();
}

/**
 * 更新选中物体显示
 */
function updateSelectedObjectDisplay() {
  const display = document.getElementById("selectedObject");
  if (selectedObject) {
    const type = selectedObject.userData.type;
    const pos = selectedObject.position;
    display.textContent = `类型: ${type}\n位置: (${pos.x.toFixed(
      2
    )}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)})`;
  } else {
    display.textContent = "未选中任何物体";
  }
}

/**
 * 更新鼠标状态显示
 */
function updateMouseStatus(event) {
  const rect = renderer.domElement.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  document.getElementById("mouseStatus").textContent = `位置: (${Math.round(
    x
  )}, ${Math.round(y)})`;
}

/**
 * 更新键盘状态显示
 */
function updateKeyboardStatus() {
  const pressedKeys = Object.keys(keys).filter((key) => keys[key]);
  const display = document.getElementById("keyboardStatus");

  if (pressedKeys.length > 0) {
    display.textContent = `按下的键: ${pressedKeys.join(", ").toUpperCase()}`;
  } else {
    display.textContent = "无按键按下";
  }
}

/**
 * 处理键盘输入移动物体
 */
function handleKeyboardInput() {
  if (!selectedObject) return;

  // WASD移动
  if (keys.w) selectedObject.position.z -= moveSpeed;
  if (keys.s) selectedObject.position.z += moveSpeed;
  if (keys.a) selectedObject.position.x -= moveSpeed;
  if (keys.d) selectedObject.position.x += moveSpeed;

  // QE上下移动
  if (keys.q) selectedObject.position.y += moveSpeed;
  if (keys.e) selectedObject.position.y -= moveSpeed;

  // 限制Y轴最小值
  if (selectedObject.position.y < 0.5) {
    selectedObject.position.y = 0.5;
  }

  updateSelectedObjectDisplay();
}

/**
 * 动画循环
 */
function animate() {
  animationId = requestAnimationFrame(animate);

  // 处理键盘输入
  handleKeyboardInput();

  // 渲染场景
  renderer.render(scene, camera);
}

/**
 * 设置控制器
 */
function setupControls() {
  // 移动速度控制
  const speedSlider = document.getElementById("moveSpeed");
  const speedValue = document.getElementById("moveSpeed-value");

  speedSlider.addEventListener("input", function (event) {
    moveSpeed = parseFloat(event.target.value);
    speedValue.textContent = moveSpeed.toFixed(2);
  });

  // 添加立方体按钮
  document.getElementById("addCube").addEventListener("click", function () {
    const x = (Math.random() - 0.5) * 10;
    const z = (Math.random() - 0.5) * 10;
    const color = Math.random() * 0xffffff;
    addCube(x, 1, z, color);
  });

  // 添加球体按钮
  document.getElementById("addSphere").addEventListener("click", function () {
    const x = (Math.random() - 0.5) * 10;
    const z = (Math.random() - 0.5) * 10;
    const color = Math.random() * 0xffffff;
    addSphere(x, 1, z, color);
  });

  // 删除物体按钮
  document
    .getElementById("deleteObject")
    .addEventListener("click", function () {
      if (selectedObject) {
        scene.remove(selectedObject);
        const index = objects.indexOf(selectedObject);
        if (index > -1) {
          objects.splice(index, 1);
        }
        selectedObject = null;
        updateSelectedObjectDisplay();
      }
    });

  // 重置场景按钮
  document.getElementById("resetScene").addEventListener("click", function () {
    // 清除所有物体
    objects.forEach((obj) => scene.remove(obj));
    objects = [];
    selectedObject = null;

    // 重新创建初始物体
    createInitialObjects();
    updateSelectedObjectDisplay();

    // 重置相机位置
    camera.position.set(0, 5, 10);
    camera.lookAt(0, 0, 0);
  });
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();

  camera.aspect = containerRect.width / containerRect.height;
  camera.updateProjectionMatrix();

  renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener("resize", onWindowResize);
window.addEventListener("load", init);
