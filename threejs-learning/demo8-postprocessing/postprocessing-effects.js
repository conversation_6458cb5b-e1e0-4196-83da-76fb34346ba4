/**
 * Three.js Demo 8: 后处理效果管线
 * 学习屏幕空间效果处理和渲染管线
 * 
 * 重点学习：
 * 1. 渲染到纹理(Render to Texture)
 * 2. 后处理通道(Post-processing Pass)
 * 3. 效果组合和管线设计
 * 4. 屏幕空间着色器
 */

// 全局变量
let scene, camera, renderer;
let renderTarget, postProcessingScene, postProcessingCamera;
let postProcessingMaterial;
let objects = [];
let animationId;

// 后处理参数
let effects = {
    bloom: { enabled: false, strength: 1.0, threshold: 0.85 },
    dof: { enabled: false, focusDistance: 10.0, aperture: 0.005 },
    toneMapping: { enabled: true, exposure: 1.0 },
    colorAdjust: { saturation: 1.0, contrast: 1.0, tint: [1.0, 1.0, 1.0] }
};

/**
 * 初始化场景
 */
function init() {
    // 创建主场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x222222);
    
    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);
    camera.lookAt(0, 0, 0);
    
    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    renderer.setSize(containerRect.width, containerRect.height);
    container.appendChild(renderer.domElement);
    
    // 创建渲染目标和后处理
    createRenderTarget();
    createPostProcessingScene();
    
    // 添加光源和场景对象
    addLights();
    createSceneObjects();
    
    // 设置控制器
    setupControls();
    
    // 开始动画循环
    animate();
}

/**
 * 创建渲染目标
 */
function createRenderTarget() {
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    
    renderTarget = new THREE.WebGLRenderTarget(containerRect.width, containerRect.height, {
        minFilter: THREE.LinearFilter,
        magFilter: THREE.LinearFilter,
        format: THREE.RGBAFormat
    });
}

/**
 * 创建后处理场景
 */
function createPostProcessingScene() {
    postProcessingScene = new THREE.Scene();
    postProcessingCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
    
    // 简化的后处理着色器
    const fragmentShader = `
        uniform sampler2D tDiffuse;
        uniform vec2 resolution;
        uniform bool bloomEnabled;
        uniform float bloomStrength;
        uniform float bloomThreshold;
        uniform bool dofEnabled;
        uniform float focusDistance;
        uniform float aperture;
        uniform bool toneMappingEnabled;
        uniform float exposure;
        uniform float saturation;
        uniform float contrast;
        uniform vec3 tint;
        
        varying vec2 vUv;
        
        void main() {
            vec2 uv = gl_FragCoord.xy / resolution;
            vec3 color = texture2D(tDiffuse, uv).rgb;
            
            // 简化的辉光效果
            if(bloomEnabled) {
                vec3 bloom = vec3(0.0);
                vec2 offset = 1.0 / resolution;
                for(int i = -1; i <= 1; i++) {
                    for(int j = -1; j <= 1; j++) {
                        vec3 sample = texture2D(tDiffuse, uv + vec2(i, j) * offset).rgb;
                        float brightness = dot(sample, vec3(0.299, 0.587, 0.114));
                        if(brightness > bloomThreshold) {
                            bloom += sample;
                        }
                    }
                }
                color += bloom * bloomStrength * 0.1;
            }
            
            // 简化的景深效果
            if(dofEnabled) {
                vec3 blur = vec3(0.0);
                for(int i = 0; i < 4; i++) {
                    float angle = float(i) * 1.57;
                    vec2 offset = vec2(cos(angle), sin(angle)) * aperture;
                    blur += texture2D(tDiffuse, uv + offset).rgb;
                }
                float depth = length(uv - vec2(0.5));
                float blurAmount = abs(depth - focusDistance * 0.1);
                color = mix(color, blur * 0.25, blurAmount);
            }
            
            // 色调映射
            if(toneMappingEnabled) {
                color *= exposure;
                color = color / (color + vec3(1.0));
            }
            
            // 颜色调整
            float gray = dot(color, vec3(0.299, 0.587, 0.114));
            color = mix(vec3(gray), color, saturation);
            color = (color - 0.5) * contrast + 0.5;
            color *= tint;
            
            gl_FragColor = vec4(color, 1.0);
        }
    `;
    
    postProcessingMaterial = new THREE.ShaderMaterial({
        uniforms: {
            tDiffuse: { value: null },
            resolution: { value: new THREE.Vector2() },
            bloomEnabled: { value: false },
            bloomStrength: { value: 1.0 },
            bloomThreshold: { value: 0.85 },
            dofEnabled: { value: false },
            focusDistance: { value: 10.0 },
            aperture: { value: 0.005 },
            toneMappingEnabled: { value: true },
            exposure: { value: 1.0 },
            saturation: { value: 1.0 },
            contrast: { value: 1.0 },
            tint: { value: new THREE.Vector3(1, 1, 1) }
        },
        vertexShader: `
            varying vec2 vUv;
            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: fragmentShader
    });
    
    const geometry = new THREE.PlaneGeometry(2, 2);
    const mesh = new THREE.Mesh(geometry, postProcessingMaterial);
    postProcessingScene.add(mesh);
}

/**
 * 添加光源
 */
function addLights() {
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(5, 10, 5);
    scene.add(directionalLight);
    
    // 彩色点光源
    const pointLight1 = new THREE.PointLight(0xff4444, 2.0, 10);
    pointLight1.position.set(-3, 2, 3);
    scene.add(pointLight1);
    
    const pointLight2 = new THREE.PointLight(0x4444ff, 2.0, 10);
    pointLight2.position.set(3, 2, -3);
    scene.add(pointLight2);
}

/**
 * 创建场景对象
 */
function createSceneObjects() {
    // 地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    scene.add(ground);
    
    // 发光立方体
    for (let i = 0; i < 5; i++) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshStandardMaterial({
            color: new THREE.Color().setHSL(i / 5, 0.8, 0.6),
            emissive: new THREE.Color().setHSL(i / 5, 0.8, 0.3)
        });
        
        const cube = new THREE.Mesh(geometry, material);
        cube.position.set((i - 2) * 2.5, 0.5, Math.sin(i) * 2);
        scene.add(cube);
        objects.push(cube);
    }
    
    // 发光球体
    for (let i = 0; i < 3; i++) {
        const geometry = new THREE.SphereGeometry(0.5, 32, 32);
        const material = new THREE.MeshStandardMaterial({
            color: 0xffffff,
            emissive: new THREE.Color().setHSL(0.1 + i * 0.3, 1.0, 0.5)
        });
        
        const sphere = new THREE.Mesh(geometry, material);
        sphere.position.set(Math.cos(i * 2) * 4, 2, Math.sin(i * 2) * 4);
        scene.add(sphere);
        objects.push(sphere);
    }
}

/**
 * 动画循环
 */
function animate() {
    animationId = requestAnimationFrame(animate);
    
    const time = Date.now() * 0.001;
    
    // 动画场景对象
    objects.forEach((obj, index) => {
        if (obj.geometry.type === 'BoxGeometry') {
            obj.rotation.x = time + index;
            obj.rotation.y = time * 0.7 + index;
        } else {
            obj.position.y = 2 + Math.sin(time + index) * 1;
        }
    });
    
    // 动画相机
    camera.position.x = Math.cos(time * 0.3) * 10;
    camera.position.z = Math.sin(time * 0.3) * 10;
    camera.lookAt(0, 1, 0);
    
    // 渲染到纹理
    renderer.setRenderTarget(renderTarget);
    renderer.render(scene, camera);
    
    // 更新后处理参数
    updatePostProcessingUniforms();
    
    // 后处理渲染到屏幕
    renderer.setRenderTarget(null);
    renderer.render(postProcessingScene, postProcessingCamera);
    
    updatePipelineInfo();
}

/**
 * 更新后处理uniform变量
 */
function updatePostProcessingUniforms() {
    const uniforms = postProcessingMaterial.uniforms;
    uniforms.tDiffuse.value = renderTarget.texture;
    uniforms.resolution.value.set(renderTarget.width, renderTarget.height);
    
    uniforms.bloomEnabled.value = effects.bloom.enabled;
    uniforms.bloomStrength.value = effects.bloom.strength;
    uniforms.bloomThreshold.value = effects.bloom.threshold;
    
    uniforms.dofEnabled.value = effects.dof.enabled;
    uniforms.focusDistance.value = effects.dof.focusDistance;
    uniforms.aperture.value = effects.dof.aperture;
    
    uniforms.toneMappingEnabled.value = effects.toneMapping.enabled;
    uniforms.exposure.value = effects.toneMapping.exposure;
    
    uniforms.saturation.value = effects.colorAdjust.saturation;
    uniforms.contrast.value = effects.colorAdjust.contrast;
    uniforms.tint.value.set(...effects.colorAdjust.tint);
}

/**
 * 更新管线信息显示
 */
function updatePipelineInfo() {
    const activeEffects = [];
    if (effects.bloom.enabled) activeEffects.push('辉光');
    if (effects.dof.enabled) activeEffects.push('景深');
    if (effects.toneMapping.enabled) activeEffects.push('色调映射');
    
    const info = activeEffects.length > 0 
        ? `渲染管线: 场景 → ${activeEffects.join(' → ')} → 屏幕`
        : '渲染管线: 场景 → 屏幕';
    
    document.getElementById('pipelineInfo').textContent = info;
}

/**
 * 设置控制器
 */
function setupControls() {
    // 辉光效果
    document.getElementById('bloomToggle').addEventListener('change', function() {
        effects.bloom.enabled = this.checked;
    });
    
    setupSlider('bloomStrength', 'bloomStrength-value', (value) => {
        effects.bloom.strength = value;
        return value.toFixed(1);
    });
    
    setupSlider('bloomThreshold', 'bloomThreshold-value', (value) => {
        effects.bloom.threshold = value;
        return value.toFixed(2);
    });
    
    // 景深效果
    document.getElementById('dofToggle').addEventListener('change', function() {
        effects.dof.enabled = this.checked;
    });
    
    setupSlider('focusDistance', 'focusDistance-value', (value) => {
        effects.dof.focusDistance = value;
        return value.toFixed(1);
    });
    
    setupSlider('aperture', 'aperture-value', (value) => {
        effects.dof.aperture = value;
        return value.toFixed(4);
    });
    
    // 色调映射
    document.getElementById('toneMappingToggle').addEventListener('change', function() {
        effects.toneMapping.enabled = this.checked;
    });
    
    setupSlider('exposure', 'exposure-value', (value) => {
        effects.toneMapping.exposure = value;
        return value.toFixed(1);
    });
    
    // 颜色调整
    setupSlider('saturation', 'saturation-value', (value) => {
        effects.colorAdjust.saturation = value;
        return value.toFixed(1);
    });
    
    setupSlider('contrast', 'contrast-value', (value) => {
        effects.colorAdjust.contrast = value;
        return value.toFixed(1);
    });
    
    document.getElementById('tintColor').addEventListener('input', function(event) {
        const color = new THREE.Color(event.target.value);
        effects.colorAdjust.tint = [color.r, color.g, color.b];
    });
    
    // 重置和预设按钮
    document.getElementById('resetEffectsButton').addEventListener('click', resetEffects);
    document.getElementById('presetButton').addEventListener('click', applyPreset);
}

/**
 * 设置滑块控制器
 */
function setupSlider(sliderId, valueId, updateFn) {
    const slider = document.getElementById(sliderId);
    const valueDisplay = document.getElementById(valueId);
    
    slider.addEventListener('input', function(event) {
        const value = parseFloat(event.target.value);
        const displayValue = updateFn(value);
        valueDisplay.textContent = displayValue;
    });
}

/**
 * 重置所有效果
 */
function resetEffects() {
    effects.bloom.enabled = false;
    effects.dof.enabled = false;
    effects.toneMapping.enabled = true;
    effects.colorAdjust.saturation = 1.0;
    effects.colorAdjust.contrast = 1.0;
    effects.colorAdjust.tint = [1.0, 1.0, 1.0];
    
    // 更新UI
    document.getElementById('bloomToggle').checked = false;
    document.getElementById('dofToggle').checked = false;
    document.getElementById('toneMappingToggle').checked = true;
    document.getElementById('saturation').value = 1.0;
    document.getElementById('contrast').value = 1.0;
    document.getElementById('tintColor').value = '#ffffff';
}

/**
 * 应用预设效果
 */
function applyPreset() {
    effects.bloom.enabled = true;
    effects.bloom.strength = 1.5;
    effects.dof.enabled = true;
    effects.colorAdjust.saturation = 1.3;
    
    document.getElementById('bloomToggle').checked = true;
    document.getElementById('dofToggle').checked = true;
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    
    camera.aspect = containerRect.width / containerRect.height;
    camera.updateProjectionMatrix();
    
    renderer.setSize(containerRect.width, containerRect.height);
    renderTarget.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener('resize', onWindowResize);
window.addEventListener('load', init);
