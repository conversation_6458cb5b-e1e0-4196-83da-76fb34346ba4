<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 8: 后处理效果管线</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .controls {
            width: 400px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .effect-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .effect-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .effect-toggle {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .effect-toggle input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .effect-toggle label {
            margin: 0;
            cursor: pointer;
        }
        
        .pipeline-info {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 10px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🎨 后处理效果管线</h1>
        <p>学习屏幕空间效果处理和渲染管线</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <h3>🎨 后处理控制</h3>
            
            <div class="effect-section">
                <h4>✨ 辉光效果 (Bloom)</h4>
                <div class="effect-toggle">
                    <input type="checkbox" id="bloomToggle">
                    <label for="bloomToggle">启用辉光</label>
                </div>
                <div class="control-group">
                    <label for="bloomStrength">辉光强度</label>
                    <input type="range" id="bloomStrength" min="0" max="3" step="0.1" value="1">
                    <div class="value-display" id="bloomStrength-value">1.0</div>
                </div>
                <div class="control-group">
                    <label for="bloomThreshold">辉光阈值</label>
                    <input type="range" id="bloomThreshold" min="0" max="1" step="0.05" value="0.85">
                    <div class="value-display" id="bloomThreshold-value">0.85</div>
                </div>
            </div>
            
            <div class="effect-section">
                <h4>🌫️ 景深效果 (DOF)</h4>
                <div class="effect-toggle">
                    <input type="checkbox" id="dofToggle">
                    <label for="dofToggle">启用景深</label>
                </div>
                <div class="control-group">
                    <label for="focusDistance">焦点距离</label>
                    <input type="range" id="focusDistance" min="1" max="20" step="0.5" value="10">
                    <div class="value-display" id="focusDistance-value">10.0</div>
                </div>
                <div class="control-group">
                    <label for="aperture">光圈大小</label>
                    <input type="range" id="aperture" min="0.0001" max="0.01" step="0.0001" value="0.005">
                    <div class="value-display" id="aperture-value">0.005</div>
                </div>
            </div>
            
            <div class="effect-section">
                <h4>🎭 色调映射 (Tone Mapping)</h4>
                <div class="effect-toggle">
                    <input type="checkbox" id="toneMappingToggle" checked>
                    <label for="toneMappingToggle">启用色调映射</label>
                </div>
                <div class="control-group">
                    <label for="exposure">曝光度</label>
                    <input type="range" id="exposure" min="0.1" max="3" step="0.1" value="1">
                    <div class="value-display" id="exposure-value">1.0</div>
                </div>
            </div>
            
            <div class="effect-section">
                <h4>🌈 颜色调整</h4>
                <div class="control-group">
                    <label for="saturation">饱和度</label>
                    <input type="range" id="saturation" min="0" max="2" step="0.1" value="1">
                    <div class="value-display" id="saturation-value">1.0</div>
                </div>
                <div class="control-group">
                    <label for="contrast">对比度</label>
                    <input type="range" id="contrast" min="0" max="2" step="0.1" value="1">
                    <div class="value-display" id="contrast-value">1.0</div>
                </div>
                <div class="control-group">
                    <label for="tintColor">色调</label>
                    <input type="color" id="tintColor" value="#ffffff">
                </div>
            </div>
            
            <div class="control-group">
                <button id="resetEffectsButton">🔄 重置所有效果</button>
                <button id="presetButton">🎨 应用预设</button>
            </div>
            
            <div class="pipeline-info" id="pipelineInfo">
                渲染管线: 场景 → 后处理 → 屏幕
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>渲染到纹理</strong>：将场景渲染到离屏缓冲区</p>
                <p><strong>后处理通道</strong>：对渲染结果进行二次处理</p>
                <p><strong>辉光效果</strong>：模拟强光的光晕现象</p>
                <p><strong>景深效果</strong>：模拟相机的焦点模糊</p>
                <p><strong>色调映射</strong>：将HDR颜色映射到显示器</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.128.0/three.min.js"></script>
    <script src="postprocessing-effects.js"></script>
</body>
</html>
