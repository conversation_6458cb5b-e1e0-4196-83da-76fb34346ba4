/**
 * Three.js Demo 9: 物理引擎集成
 * 学习真实物理模拟和碰撞检测
 * 
 * 重点学习：
 * 1. Cannon.js物理引擎集成
 * 2. 刚体物理和碰撞检测
 * 3. 约束和关节系统
 * 4. 物理材质和摩擦力
 */

// 全局变量
let scene, camera, renderer;
let world;              // 物理世界
let physicsObjects = []; // 物理对象数组
let animationId;
let clock;

// 物理参数
let physicsParams = {
    gravity: 9.8,
    mass: 1.0,
    restitution: 0.3,
    friction: 0.4,
    contactStiffness: 5000,
    isPaused: false,
    windForce: false
};

// 材质
let physicsMaterial;
let groundMaterial;

/**
 * 初始化场景
 */
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB);
    
    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(10, 10, 10);
    camera.lookAt(0, 0, 0);
    
    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    renderer.setSize(containerRect.width, containerRect.height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(renderer.domElement);
    
    // 创建时钟
    clock = new THREE.Clock();
    
    // 初始化物理世界
    initPhysicsWorld();
    
    // 添加光源
    addLights();
    
    // 创建地面
    createGround();
    
    // 创建墙壁
    createWalls();
    
    // 设置控制器
    setupControls();
    
    // 开始动画循环
    animate();
}

/**
 * 初始化物理世界
 */
function initPhysicsWorld() {
    // 创建物理世界
    world = new CANNON.World();
    world.gravity.set(0, -physicsParams.gravity, 0);
    world.broadphase = new CANNON.NaiveBroadphase();
    
    // 创建物理材质
    physicsMaterial = new CANNON.Material('physics');
    groundMaterial = new CANNON.Material('ground');
    
    // 设置材质间的接触属性
    const contactMaterial = new CANNON.ContactMaterial(
        physicsMaterial,
        groundMaterial,
        {
            friction: physicsParams.friction,
            restitution: physicsParams.restitution,
            contactEquationStiffness: physicsParams.contactStiffness
        }
    );
    world.addContactMaterial(contactMaterial);
}

/**
 * 添加光源
 */
function addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);
    
    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -20;
    directionalLight.shadow.camera.right = 20;
    directionalLight.shadow.camera.top = 20;
    directionalLight.shadow.camera.bottom = -20;
    scene.add(directionalLight);
}

/**
 * 创建地面
 */
function createGround() {
    // Three.js地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x90EE90,
        transparent: true,
        opacity: 0.8
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.receiveShadow = true;
    scene.add(groundMesh);
    
    // Cannon.js地面
    const groundShape = new CANNON.Plane();
    const groundBody = new CANNON.Body({ mass: 0, material: groundMaterial });
    groundBody.addShape(groundShape);
    groundBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);
    world.add(groundBody);
}

/**
 * 创建墙壁
 */
function createWalls() {
    const wallHeight = 5;
    const wallThickness = 0.5;
    const roomSize = 10;
    
    // 创建四面墙
    const walls = [
        { pos: [0, wallHeight/2, roomSize], size: [roomSize, wallHeight, wallThickness] },
        { pos: [0, wallHeight/2, -roomSize], size: [roomSize, wallHeight, wallThickness] },
        { pos: [roomSize, wallHeight/2, 0], size: [wallThickness, wallHeight, roomSize] },
        { pos: [-roomSize, wallHeight/2, 0], size: [wallThickness, wallHeight, roomSize] }
    ];
    
    walls.forEach(wall => {
        // Three.js墙壁
        const geometry = new THREE.BoxGeometry(...wall.size);
        const material = new THREE.MeshLambertMaterial({ 
            color: 0xcccccc,
            transparent: true,
            opacity: 0.3
        });
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(...wall.pos);
        scene.add(mesh);
        
        // Cannon.js墙壁
        const shape = new CANNON.Box(new CANNON.Vec3(...wall.size.map(s => s/2)));
        const body = new CANNON.Body({ mass: 0 });
        body.addShape(shape);
        body.position.set(...wall.pos);
        world.add(body);
    });
}

/**
 * 创建物理对象
 */
function createPhysicsObject(type) {
    let geometry, shape;
    const size = 0.5 + Math.random() * 0.5;
    
    switch (type) {
        case 'box':
            geometry = new THREE.BoxGeometry(size, size, size);
            shape = new CANNON.Box(new CANNON.Vec3(size/2, size/2, size/2));
            break;
        case 'sphere':
            geometry = new THREE.SphereGeometry(size, 16, 16);
            shape = new CANNON.Sphere(size);
            break;
        case 'cylinder':
            geometry = new THREE.CylinderGeometry(size, size, size * 2, 8);
            shape = new CANNON.Cylinder(size, size, size * 2, 8);
            break;
        case 'compound':
            // 复合形状：多个小立方体组成
            geometry = new THREE.Group();
            const positions = [
                [0, 0, 0], [size, 0, 0], [0, size, 0], [0, 0, size]
            ];
            positions.forEach(pos => {
                const boxGeo = new THREE.BoxGeometry(size/2, size/2, size/2);
                const boxMesh = new THREE.Mesh(boxGeo);
                boxMesh.position.set(...pos);
                geometry.add(boxMesh);
            });
            
            shape = new CANNON.Compound();
            positions.forEach(pos => {
                const boxShape = new CANNON.Box(new CANNON.Vec3(size/4, size/4, size/4));
                shape.addShape(boxShape, new CANNON.Vec3(...pos));
            });
            break;
    }
    
    // Three.js网格
    const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color().setHSL(Math.random(), 0.8, 0.6),
        roughness: 0.3,
        metalness: 0.7
    });
    
    let mesh;
    if (type === 'compound') {
        mesh = geometry;
        mesh.children.forEach(child => {
            child.material = material;
            child.castShadow = true;
        });
    } else {
        mesh = new THREE.Mesh(geometry, material);
        mesh.castShadow = true;
    }
    
    // 随机位置
    const x = (Math.random() - 0.5) * 8;
    const y = 5 + Math.random() * 5;
    const z = (Math.random() - 0.5) * 8;
    mesh.position.set(x, y, z);
    scene.add(mesh);
    
    // Cannon.js刚体
    const body = new CANNON.Body({ 
        mass: physicsParams.mass,
        material: physicsMaterial
    });
    body.addShape(shape);
    body.position.set(x, y, z);
    
    // 添加随机旋转
    body.angularVelocity.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
    );
    
    world.add(body);
    
    // 存储对象
    physicsObjects.push({ mesh, body, type });
}

/**
 * 应用爆炸力
 */
function applyExplosion() {
    const explosionCenter = new CANNON.Vec3(0, 2, 0);
    const explosionForce = 50;
    
    physicsObjects.forEach(obj => {
        const direction = obj.body.position.vsub(explosionCenter);
        const distance = direction.length();
        
        if (distance < 8) {
            direction.normalize();
            const force = direction.scale(explosionForce / (distance + 1));
            obj.body.applyImpulse(force, obj.body.position);
        }
    });
}

/**
 * 应用风力
 */
function applyWind() {
    if (!physicsParams.windForce) return;
    
    const windForce = new CANNON.Vec3(5, 0, 0);
    physicsObjects.forEach(obj => {
        obj.body.applyForce(windForce, obj.body.position);
    });
}

/**
 * 动画循环
 */
function animate() {
    animationId = requestAnimationFrame(animate);
    
    const deltaTime = clock.getDelta();
    
    // 更新物理世界
    if (!physicsParams.isPaused) {
        // 应用风力
        applyWind();
        
        // 步进物理模拟
        world.step(deltaTime);
        
        // 同步Three.js对象与物理对象
        physicsObjects.forEach(obj => {
            obj.mesh.position.copy(obj.body.position);
            obj.mesh.quaternion.copy(obj.body.quaternion);
        });
        
        // 清理掉落的对象
        physicsObjects = physicsObjects.filter(obj => {
            if (obj.body.position.y < -10) {
                scene.remove(obj.mesh);
                world.remove(obj.body);
                return false;
            }
            return true;
        });
    }
    
    // 更新统计信息
    updateStats();
    
    // 渲染场景
    renderer.render(scene, camera);
}

/**
 * 更新统计信息
 */
function updateStats() {
    const stats = `物理对象: ${physicsObjects.length}
碰撞检测: ${physicsParams.isPaused ? '暂停' : '启用'}
重力: ${physicsParams.gravity} m/s²`;
    
    document.getElementById('physicsStats').textContent = stats;
}

/**
 * 设置控制器
 */
function setupControls() {
    // 重力控制
    setupSlider('gravity', 'gravity-value', (value) => {
        physicsParams.gravity = value;
        world.gravity.set(0, -value, 0);
        return `${value} m/s²`;
    });
    
    // 质量控制
    setupSlider('mass', 'mass-value', (value) => {
        physicsParams.mass = value;
        return `${value} kg`;
    });
    
    // 弹性系数
    setupSlider('restitution', 'restitution-value', (value) => {
        physicsParams.restitution = value;
        return value.toFixed(1);
    });
    
    // 摩擦系数
    setupSlider('friction', 'friction-value', (value) => {
        physicsParams.friction = value;
        return value.toFixed(1);
    });
    
    // 接触刚度
    setupSlider('contactStiffness', 'contactStiffness-value', (value) => {
        physicsParams.contactStiffness = value;
        return value.toString();
    });
    
    // 生成物体按钮
    document.getElementById('spawnBox').addEventListener('click', () => createPhysicsObject('box'));
    document.getElementById('spawnSphere').addEventListener('click', () => createPhysicsObject('sphere'));
    document.getElementById('spawnCylinder').addEventListener('click', () => createPhysicsObject('cylinder'));
    document.getElementById('spawnCompound').addEventListener('click', () => createPhysicsObject('compound'));
    
    // 场景控制按钮
    const pauseButton = document.getElementById('pausePhysics');
    pauseButton.addEventListener('click', function() {
        physicsParams.isPaused = !physicsParams.isPaused;
        this.textContent = physicsParams.isPaused ? '▶️ 继续物理' : '⏸️ 暂停物理';
        this.classList.toggle('active', physicsParams.isPaused);
    });
    
    document.getElementById('resetScene').addEventListener('click', function() {
        // 清空所有物体
        physicsObjects.forEach(obj => {
            scene.remove(obj.mesh);
            world.remove(obj.body);
        });
        physicsObjects = [];
        
        // 重置物理世界
        world.gravity.set(0, -physicsParams.gravity, 0);
    });
    
    document.getElementById('clearObjects').addEventListener('click', function() {
        physicsObjects.forEach(obj => {
            scene.remove(obj.mesh);
            world.remove(obj.body);
        });
        physicsObjects = [];
    });
    
    // 力效果按钮
    const windButton = document.getElementById('addForce');
    windButton.addEventListener('click', function() {
        physicsParams.windForce = !physicsParams.windForce;
        this.textContent = physicsParams.windForce ? '🌪️ 停止风力' : '💨 添加风力';
        this.classList.toggle('active', physicsParams.windForce);
    });
    
    document.getElementById('explosion').addEventListener('click', applyExplosion);
}

/**
 * 设置滑块控制器
 */
function setupSlider(sliderId, valueId, updateFn) {
    const slider = document.getElementById(sliderId);
    const valueDisplay = document.getElementById(valueId);
    
    slider.addEventListener('input', function(event) {
        const value = parseFloat(event.target.value);
        const displayValue = updateFn(value);
        valueDisplay.textContent = displayValue;
    });
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    
    camera.aspect = containerRect.width / containerRect.height;
    camera.updateProjectionMatrix();
    
    renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener('resize', onWindowResize);
window.addEventListener('load', init);
