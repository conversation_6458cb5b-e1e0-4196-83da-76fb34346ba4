<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 9: 物理引擎集成</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .controls {
            width: 380px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .physics-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .physics-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .stats-display {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .spawn-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .spawn-buttons button {
            padding: 8px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>⚡ 物理引擎集成</h1>
        <p>学习真实物理模拟和碰撞检测</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <h3>⚡ 物理控制</h3>
            
            <div class="physics-section">
                <h4>🌍 重力设置</h4>
                <div class="control-group">
                    <label for="gravity">重力强度</label>
                    <input type="range" id="gravity" min="0" max="30" step="1" value="9.8">
                    <div class="value-display" id="gravity-value">9.8 m/s²</div>
                </div>
            </div>
            
            <div class="physics-section">
                <h4>📦 添加物体</h4>
                <div class="spawn-buttons">
                    <button id="spawnBox">📦 立方体</button>
                    <button id="spawnSphere">⚽ 球体</button>
                    <button id="spawnCylinder">🥫 圆柱</button>
                    <button id="spawnCompound">🎲 复合体</button>
                </div>
                
                <div class="control-group">
                    <label for="mass">物体质量</label>
                    <input type="range" id="mass" min="0.1" max="10" step="0.1" value="1">
                    <div class="value-display" id="mass-value">1.0 kg</div>
                </div>
                
                <div class="control-group">
                    <label for="restitution">弹性系数</label>
                    <input type="range" id="restitution" min="0" max="1" step="0.1" value="0.3">
                    <div class="value-display" id="restitution-value">0.3</div>
                </div>
            </div>
            
            <div class="physics-section">
                <h4>🎮 场景控制</h4>
                <div class="control-group">
                    <button id="pausePhysics">⏸️ 暂停物理</button>
                    <button id="resetScene">🔄 重置场景</button>
                    <button id="clearObjects">🗑️ 清空物体</button>
                </div>
                
                <div class="control-group">
                    <button id="addForce">💨 添加风力</button>
                    <button id="explosion">💥 爆炸效果</button>
                </div>
            </div>
            
            <div class="physics-section">
                <h4>🔧 材质属性</h4>
                <div class="control-group">
                    <label for="friction">摩擦系数</label>
                    <input type="range" id="friction" min="0" max="1" step="0.1" value="0.4">
                    <div class="value-display" id="friction-value">0.4</div>
                </div>
                
                <div class="control-group">
                    <label for="contactStiffness">接触刚度</label>
                    <input type="range" id="contactStiffness" min="1000" max="10000" step="500" value="5000">
                    <div class="value-display" id="contactStiffness-value">5000</div>
                </div>
            </div>
            
            <div class="stats-display" id="physicsStats">
                物理对象: 0
                碰撞检测: 启用
                计算时间: 0ms
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>刚体物理</strong>：模拟真实的物体运动</p>
                <p><strong>碰撞检测</strong>：检测物体间的接触</p>
                <p><strong>约束系统</strong>：限制物体的运动</p>
                <p><strong>材质属性</strong>：摩擦力、弹性、密度</p>
                <p><strong>力的应用</strong>：重力、推力、扭矩</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.128.0/three.min.js"></script>
    <!-- Cannon.js 物理引擎 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cannon.js/0.20.0/cannon.min.js"></script>
    <script src="physics-simulation.js"></script>
</body>
</html>
