/**
 * Three.js Demo 4: 纹理和贴图
 * 学习纹理加载、UV映射和不同类型的贴图
 *
 * 重点学习：
 * 1. 程序化纹理的创建
 * 2. UV坐标的控制
 * 3. 纹理包装和过滤模式
 * 4. 纹理的重复和偏移
 */

// 全局变量
let scene, camera, renderer;
let mesh;
let currentTexture;
let animationId;

// 控制参数
let isRotating = true;
let currentGeometry = "box";
let geometries = {};

/**
 * 初始化场景
 */
function init() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  scene.add(camera);
  camera.position.set(0, 0, 5);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();
  renderer.setSize(containerRect.width, containerRect.height);
  container.appendChild(renderer.domElement);

  // 添加光源
  addLights();

  // 创建几何体
  createGeometries();

  // 创建默认纹理和对象
  createTexture("procedural");

  // 设置控制器
  setupControls();

  // 开始动画循环
  animate();
}

/**
 * 添加光源
 */
function addLights() {
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(5, 5, 5);
  scene.add(directionalLight);
}

/**
 * 创建不同的几何体
 */
function createGeometries() {
  // 立方体
  geometries.box = new THREE.BoxGeometry(2, 2, 2);

  // 球体
  geometries.sphere = new THREE.SphereGeometry(1.5, 32, 32);

  // 圆柱体
  geometries.cylinder = new THREE.CylinderGeometry(1, 1, 2, 32);

  // 平面
  geometries.plane = new THREE.PlaneGeometry(3, 3);
}

/**
 * 创建程序化纹理
 * @param {string} type - 纹理类型
 */
function createTexture(type) {
  let canvas, context, imageData;
  const size = 256;

  // 创建canvas
  canvas = document.createElement("canvas");
  canvas.width = size;
  canvas.height = size;
  context = canvas.getContext("2d");

  switch (type) {
    case "procedural":
      // 创建简单的程序化纹理
      createProceduralTexture(context, size);
      break;

    case "checker":
      // 创建棋盘格纹理
      createCheckerTexture(context, size);
      break;

    case "gradient":
      // 创建渐变纹理
      createGradientTexture(context, size);
      break;

    case "noise":
      // 创建噪声纹理
      createNoiseTexture(context, size);
      break;
  }

  // 创建Three.js纹理
  currentTexture = new THREE.CanvasTexture(canvas);

  // 设置纹理参数
  currentTexture.wrapS = THREE.RepeatWrapping;
  currentTexture.wrapT = THREE.RepeatWrapping;
  currentTexture.magFilter = THREE.LinearFilter;
  currentTexture.minFilter = THREE.LinearFilter;

  // 更新预览
  updateTexturePreview(canvas);

  // 创建或更新3D对象
  createMesh();
}

/**
 * 创建程序化纹理
 */
function createProceduralTexture(context, size) {
  const imageData = context.createImageData(size, size);
  const data = imageData.data;

  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      const index = (i * size + j) * 4;

      // 创建彩色条纹图案
      const x = i / size;
      const y = j / size;

      const r = Math.sin(x * Math.PI * 8) * 127 + 128;
      const g = Math.sin(y * Math.PI * 8) * 127 + 128;
      const b = Math.sin((x + y) * Math.PI * 4) * 127 + 128;

      data[index] = r; // R
      data[index + 1] = g; // G
      data[index + 2] = b; // B
      data[index + 3] = 255; // A
    }
  }

  context.putImageData(imageData, 0, 0);
}

/**
 * 创建棋盘格纹理
 */
function createCheckerTexture(context, size) {
  const squareSize = size / 8;

  for (let i = 0; i < 8; i++) {
    for (let j = 0; j < 8; j++) {
      const isEven = (i + j) % 2 === 0;
      context.fillStyle = isEven ? "#ffffff" : "#000000";
      context.fillRect(i * squareSize, j * squareSize, squareSize, squareSize);
    }
  }
}

/**
 * 创建渐变纹理
 */
function createGradientTexture(context, size) {
  const gradient = context.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, "#ff6b6b");
  gradient.addColorStop(0.5, "#4ecdc4");
  gradient.addColorStop(1, "#45b7d1");

  context.fillStyle = gradient;
  context.fillRect(0, 0, size, size);
}

/**
 * 创建噪声纹理
 */
function createNoiseTexture(context, size) {
  const imageData = context.createImageData(size, size);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    const noise = Math.random() * 255;
    data[i] = noise; // R
    data[i + 1] = noise; // G
    data[i + 2] = noise; // B
    data[i + 3] = 255; // A
  }

  context.putImageData(imageData, 0, 0);
}

/**
 * 更新纹理预览
 */
function updateTexturePreview(canvas) {
  const preview = document.getElementById("texturePreview");
  preview.style.backgroundImage = `url(${canvas.toDataURL()})`;
}

/**
 * 创建3D网格对象
 */
function createMesh() {
  // 移除旧对象
  if (mesh) {
    scene.remove(mesh);
    mesh.geometry.dispose();
    mesh.material.dispose();
  }

  // 创建材质
  const material = new THREE.MeshStandardMaterial({
    map: currentTexture,
    metalness: 0.3,
    roughness: 0.4,
  });

  // 创建网格
  mesh = new THREE.Mesh(geometries[currentGeometry], material);
  scene.add(mesh);
}

/**
 * 动画循环
 */
function animate() {
  animationId = requestAnimationFrame(animate);

  // 旋转对象
  if (isRotating && mesh) {
    mesh.rotation.x += 0.01;
    mesh.rotation.y += 0.01;
  }

  // 渲染场景
  renderer.render(scene, camera);
}

/**
 * 设置控制器
 */
function setupControls() {
  // 纹理类型选择
  const textureSelect = document.getElementById("textureType");
  textureSelect.addEventListener("change", function (event) {
    createTexture(event.target.value);
  });

  // UV重复控制
  setupSlider("repeatU", "repeatU-value", (value) => {
    if (currentTexture) {
      currentTexture.repeat.x = value;
      currentTexture.needsUpdate = true;
    }
    return value.toFixed(1);
  });

  setupSlider("repeatV", "repeatV-value", (value) => {
    if (currentTexture) {
      currentTexture.repeat.y = value;
      currentTexture.needsUpdate = true;
    }
    return value.toFixed(1);
  });

  // UV偏移控制
  setupSlider("offsetU", "offsetU-value", (value) => {
    if (currentTexture) {
      currentTexture.offset.x = value;
      currentTexture.needsUpdate = true;
    }
    return value.toFixed(1);
  });

  setupSlider("offsetV", "offsetV-value", (value) => {
    if (currentTexture) {
      currentTexture.offset.y = value;
      currentTexture.needsUpdate = true;
    }
    return value.toFixed(1);
  });

  // 包装模式
  const wrapSelect = document.getElementById("wrapMode");
  wrapSelect.addEventListener("change", function (event) {
    if (currentTexture) {
      const wrapMode = getWrapMode(event.target.value);
      currentTexture.wrapS = wrapMode;
      currentTexture.wrapT = wrapMode;
      currentTexture.needsUpdate = true;
    }
  });

  // 过滤模式
  const filterSelect = document.getElementById("filterMode");
  filterSelect.addEventListener("change", function (event) {
    if (currentTexture) {
      const filterMode = getFilterMode(event.target.value);
      currentTexture.magFilter = filterMode;
      currentTexture.minFilter = filterMode;
      currentTexture.needsUpdate = true;
    }
  });

  // 旋转控制
  const rotationButton = document.getElementById("rotationButton");
  rotationButton.addEventListener("click", function () {
    isRotating = !isRotating;
    rotationButton.classList.toggle("active", isRotating);
    rotationButton.textContent = isRotating ? "⏸️ 停止旋转" : "🔄 自动旋转";
  });

  // 几何体切换
  const geometryButton = document.getElementById("geometryButton");
  const geometryTypes = ["box", "sphere", "cylinder", "plane"];
  let geometryIndex = 0;

  geometryButton.addEventListener("click", function () {
    geometryIndex = (geometryIndex + 1) % geometryTypes.length;
    currentGeometry = geometryTypes[geometryIndex];
    createMesh();

    const names = {
      box: "立方体",
      sphere: "球体",
      cylinder: "圆柱体",
      plane: "平面",
    };
    geometryButton.textContent = `🔄 ${names[currentGeometry]}`;
  });
}

/**
 * 设置滑块控制器
 */
function setupSlider(sliderId, valueId, updateFn) {
  const slider = document.getElementById(sliderId);
  const valueDisplay = document.getElementById(valueId);

  slider.addEventListener("input", function (event) {
    const value = parseFloat(event.target.value);
    const displayValue = updateFn(value);
    valueDisplay.textContent = displayValue;
  });
}

/**
 * 获取包装模式
 */
function getWrapMode(mode) {
  switch (mode) {
    case "repeat":
      return THREE.RepeatWrapping;
    case "clamp":
      return THREE.ClampToEdgeWrapping;
    case "mirror":
      return THREE.MirroredRepeatWrapping;
    default:
      return THREE.RepeatWrapping;
  }
}

/**
 * 获取过滤模式
 */
function getFilterMode(mode) {
  switch (mode) {
    case "linear":
      return THREE.LinearFilter;
    case "nearest":
      return THREE.NearestFilter;
    default:
      return THREE.LinearFilter;
  }
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();

  camera.aspect = containerRect.width / containerRect.height;
  camera.updateProjectionMatrix();

  renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener("resize", onWindowResize);
window.addEventListener("load", init);
