<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 4: 纹理和贴图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .controls {
            width: 380px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            background: white;
            margin-bottom: 10px;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .texture-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .texture-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .texture-preview {
            width: 100%;
            height: 80px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            margin-top: 10px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🖼️ 纹理和贴图</h1>
        <p>学习纹理加载、UV映射和不同类型的贴图</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <h3>🎨 纹理控制</h3>
            
            <div class="texture-section">
                <h4>🖼️ 纹理类型</h4>
                <div class="control-group">
                    <label for="textureType">选择纹理</label>
                    <select id="textureType">
                        <option value="procedural">程序化纹理</option>
                        <option value="checker">棋盘格纹理</option>
                        <option value="gradient">渐变纹理</option>
                        <option value="noise">噪声纹理</option>
                    </select>
                    <div class="texture-preview" id="texturePreview"></div>
                </div>
            </div>
            
            <div class="texture-section">
                <h4>📐 UV映射控制</h4>
                <div class="control-group">
                    <label for="repeatU">U轴重复</label>
                    <input type="range" id="repeatU" min="0.1" max="5" step="0.1" value="1">
                    <div class="value-display" id="repeatU-value">1.0</div>
                </div>
                
                <div class="control-group">
                    <label for="repeatV">V轴重复</label>
                    <input type="range" id="repeatV" min="0.1" max="5" step="0.1" value="1">
                    <div class="value-display" id="repeatV-value">1.0</div>
                </div>
                
                <div class="control-group">
                    <label for="offsetU">U轴偏移</label>
                    <input type="range" id="offsetU" min="-2" max="2" step="0.1" value="0">
                    <div class="value-display" id="offsetU-value">0.0</div>
                </div>
                
                <div class="control-group">
                    <label for="offsetV">V轴偏移</label>
                    <input type="range" id="offsetV" min="-2" max="2" step="0.1" value="0">
                    <div class="value-display" id="offsetV-value">0.0</div>
                </div>
            </div>
            
            <div class="texture-section">
                <h4>⚙️ 纹理设置</h4>
                <div class="control-group">
                    <label for="wrapMode">包装模式</label>
                    <select id="wrapMode">
                        <option value="repeat">重复 (RepeatWrapping)</option>
                        <option value="clamp">夹紧 (ClampToEdgeWrapping)</option>
                        <option value="mirror">镜像 (MirroredRepeatWrapping)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="filterMode">过滤模式</label>
                    <select id="filterMode">
                        <option value="linear">线性 (LinearFilter)</option>
                        <option value="nearest">最近邻 (NearestFilter)</option>
                    </select>
                </div>
            </div>
            
            <div class="control-group">
                <button id="rotationButton" class="active">🔄 自动旋转</button>
                <button id="geometryButton">🔄 切换几何体</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>纹理(Texture)</strong>：贴在3D表面上的2D图像</p>
                <p><strong>UV坐标</strong>：纹理坐标系统，U(0-1)对应X轴，V(0-1)对应Y轴</p>
                <p><strong>包装模式</strong>：决定UV超出0-1范围时的处理方式</p>
                <p><strong>过滤模式</strong>：决定纹理缩放时的插值方法</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="texture-mapping.js"></script>
</body>
</html>
