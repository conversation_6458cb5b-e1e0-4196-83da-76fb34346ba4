<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 5: 动画系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .controls {
            width: 350px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .animation-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .animation-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🎬 动画系统</h1>
        <p>学习Three.js的动画系统和补间动画</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <h3>🎬 动画控制</h3>
            
            <div class="animation-section">
                <h4>🔄 基础动画</h4>
                <div class="control-group">
                    <button id="rotationAnim" class="active">旋转动画</button>
                    <button id="scaleAnim">缩放动画</button>
                    <button id="bounceAnim">弹跳动画</button>
                </div>
            </div>
            
            <div class="animation-section">
                <h4>⚡ 补间动画</h4>
                <div class="control-group">
                    <button id="moveAnim">移动动画</button>
                    <button id="colorAnim">颜色动画</button>
                    <button id="morphAnim">形变动画</button>
                </div>
            </div>
            
            <div class="control-group">
                <label for="animSpeed">动画速度</label>
                <input type="range" id="animSpeed" min="0.1" max="3" step="0.1" value="1">
                <div class="value-display" id="animSpeed-value">1.0x</div>
            </div>
            
            <div class="control-group">
                <button id="pauseButton">⏸️ 暂停动画</button>
                <button id="resetButton">🔄 重置动画</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>requestAnimationFrame</strong>：浏览器优化的动画循环</p>
                <p><strong>补间动画</strong>：在关键帧之间自动插值</p>
                <p><strong>缓动函数</strong>：控制动画的速度变化</p>
                <p><strong>动画混合</strong>：同时播放多个动画</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="animation-system.js"></script>
</body>
</html>
