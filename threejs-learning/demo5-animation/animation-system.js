/**
 * Three.js Demo 5: 动画系统
 * 学习Three.js的动画系统和补间动画
 *
 * 重点学习：
 * 1. requestAnimationFrame动画循环
 * 2. 简单的补间动画实现
 * 3. 缓动函数的使用
 * 4. 多种动画效果的组合
 */

// 全局变量
let scene, camera, renderer;
let cube;
let animationId;

// 动画控制
let animationSpeed = 1;
let isPaused = false;
let currentAnimation = "rotation";

// 动画状态
let animationTime = 0;
let animationStartTime = 0;

/**
 * 初始化场景
 */
function init() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(0, 0, 5);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();
  renderer.setSize(containerRect.width, containerRect.height);
  container.appendChild(renderer.domElement);

  // 添加光源
  addLights();

  // 创建立方体
  createCube();

  // 设置控制器
  setupControls();

  // 开始动画循环
  animate();
}

/**
 * 添加光源
 */
function addLights() {
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(5, 5, 5);
  scene.add(directionalLight);
}

/**
 * 创建立方体
 */
function createCube() {
  const geometry = new THREE.BoxGeometry(1, 1, 1);
  const material = new THREE.MeshStandardMaterial({
    color: 0x3498db,
    metalness: 0.3,
    roughness: 0.4,
  });

  cube = new THREE.Mesh(geometry, material);
  scene.add(cube);
}

/**
 * 缓动函数
 * 这些函数控制动画的速度变化，让动画更自然
 */
const Easing = {
  // 线性：匀速运动
  linear: function (t) {
    return t;
  },

  // 缓入：开始慢，后面快
  easeIn: function (t) {
    return t * t;
  },

  // 缓出：开始快，后面慢
  easeOut: function (t) {
    return 1 - (1 - t) * (1 - t);
  },

  // 缓入缓出：开始慢，中间快，结束慢
  easeInOut: function (t) {
    return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
  },

  // 弹性：有弹跳效果
  elastic: function (t) {
    return Math.sin(((13 * Math.PI) / 2) * t) * Math.pow(2, 10 * (t - 1));
  },
};

/**
 * 动画循环
 */
function animate() {
  animationId = requestAnimationFrame(animate);

  if (!isPaused) {
    // 更新动画时间
    animationTime += 0.016 * animationSpeed; // 假设60fps

    // 执行当前动画
    updateAnimation();
  }

  // 渲染场景
  renderer.render(scene, camera);
}

/**
 * 更新动画
 */
function updateAnimation() {
  switch (currentAnimation) {
    case "rotation":
      updateRotationAnimation();
      break;
    case "scale":
      updateScaleAnimation();
      break;
    case "bounce":
      updateBounceAnimation();
      break;
    case "move":
      updateMoveAnimation();
      break;
    case "color":
      updateColorAnimation();
      break;
    case "morph":
      updateMorphAnimation();
      break;
  }
}

/**
 * 旋转动画
 */
function updateRotationAnimation() {
  cube.rotation.x = animationTime * 0.5;
  cube.rotation.y = animationTime * 0.3;
  cube.rotation.z = animationTime * 0.2;
}

/**
 * 缩放动画
 */
function updateScaleAnimation() {
  // 使用正弦函数创建周期性缩放
  const scale = 1 + 0.5 * Math.sin(animationTime * 2);
  cube.scale.set(scale, scale, scale);
}

/**
 * 弹跳动画
 */
function updateBounceAnimation() {
  // 使用绝对值的正弦函数创建弹跳效果
  const bounceHeight = Math.abs(Math.sin(animationTime * 3)) * 2;
  cube.position.y = bounceHeight;

  // 添加轻微的旋转
  cube.rotation.y = animationTime * 0.5;
}

/**
 * 移动动画
 */
function updateMoveAnimation() {
  // 创建8字形运动轨迹
  const t = animationTime * 0.5;
  cube.position.x = Math.sin(t) * 2;
  cube.position.y = Math.sin(t * 2) * 1;
  cube.position.z = 0;

  // 让立方体面向运动方向
  cube.lookAt(
    cube.position.x + Math.cos(t) * 0.1,
    cube.position.y + Math.cos(t * 2) * 0.1,
    cube.position.z
  );
}

/**
 * 颜色动画
 */
function updateColorAnimation() {
  // 使用HSL颜色空间创建彩虹效果
  const hue = (animationTime * 50) % 360;
  const color = new THREE.Color().setHSL(hue / 360, 0.7, 0.5);
  cube.material.color = color;

  // 添加轻微旋转
  cube.rotation.y = animationTime * 0.3;
}

/**
 * 形变动画
 */
function updateMorphAnimation() {
  // 动态改变立方体的几何形状
  const time = animationTime * 2;

  // 创建新的几何体
  const width = 1 + 0.5 * Math.sin(time);
  const height = 1 + 0.5 * Math.sin(time + Math.PI / 3);
  const depth = 1 + 0.5 * Math.sin(time + (Math.PI * 2) / 3);

  // 移除旧几何体并创建新的
  cube.geometry.dispose();
  cube.geometry = new THREE.BoxGeometry(width, height, depth);

  // 添加旋转
  cube.rotation.x = time * 0.2;
  cube.rotation.y = time * 0.3;
}

/**
 * 设置控制器
 */
function setupControls() {
  // 基础动画按钮
  document.getElementById("rotationAnim").addEventListener("click", () => {
    setAnimation("rotation");
  });

  document.getElementById("scaleAnim").addEventListener("click", () => {
    setAnimation("scale");
  });

  document.getElementById("bounceAnim").addEventListener("click", () => {
    setAnimation("bounce");
  });

  // 补间动画按钮
  document.getElementById("moveAnim").addEventListener("click", () => {
    setAnimation("move");
  });

  document.getElementById("colorAnim").addEventListener("click", () => {
    setAnimation("color");
  });

  document.getElementById("morphAnim").addEventListener("click", () => {
    setAnimation("morph");
  });

  // 动画速度控制
  const speedSlider = document.getElementById("animSpeed");
  const speedValue = document.getElementById("animSpeed-value");

  speedSlider.addEventListener("input", function (event) {
    animationSpeed = parseFloat(event.target.value);
    speedValue.textContent = `${animationSpeed}x`;
  });

  // 暂停/继续按钮
  const pauseButton = document.getElementById("pauseButton");
  pauseButton.addEventListener("click", function () {
    isPaused = !isPaused;
    pauseButton.textContent = isPaused ? "▶️ 继续动画" : "⏸️ 暂停动画";
    pauseButton.classList.toggle("active", isPaused);
  });

  // 重置按钮
  const resetButton = document.getElementById("resetButton");
  resetButton.addEventListener("click", function () {
    resetAnimation();
  });
}

/**
 * 设置当前动画
 */
function setAnimation(animationType) {
  currentAnimation = animationType;
  animationTime = 0;

  // 重置立方体状态
  resetCubeState();

  // 更新按钮状态
  updateButtonStates(animationType);
}

/**
 * 重置立方体状态
 */
function resetCubeState() {
  cube.position.set(0, 0, 0);
  cube.rotation.set(0, 0, 0);
  cube.scale.set(1, 1, 1);

  // 重置颜色
  cube.material.color.setHex(0x3498db);

  // 重置几何体
  cube.geometry.dispose();
  cube.geometry = new THREE.BoxGeometry(1, 1, 1);
}

/**
 * 更新按钮状态
 */
function updateButtonStates(activeAnimation) {
  // 移除所有按钮的active类
  document.querySelectorAll(".animation-section button").forEach((button) => {
    button.classList.remove("active");
  });

  // 为当前动画按钮添加active类
  const buttonMap = {
    rotation: "rotationAnim",
    scale: "scaleAnim",
    bounce: "bounceAnim",
    move: "moveAnim",
    color: "colorAnim",
    morph: "morphAnim",
  };

  const activeButton = document.getElementById(buttonMap[activeAnimation]);
  if (activeButton) {
    activeButton.classList.add("active");
  }
}

/**
 * 重置动画
 */
function resetAnimation() {
  animationTime = 0;
  animationSpeed = 1;
  isPaused = false;

  // 重置UI
  document.getElementById("animSpeed").value = 1;
  document.getElementById("animSpeed-value").textContent = "1.0x";
  document.getElementById("pauseButton").textContent = "⏸️ 暂停动画";
  document.getElementById("pauseButton").classList.remove("active");

  // 重置立方体
  resetCubeState();

  // 设置默认动画
  setAnimation("rotation");
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();

  camera.aspect = containerRect.width / containerRect.height;
  camera.updateProjectionMatrix();

  renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener("resize", onWindowResize);
window.addEventListener("load", init);
