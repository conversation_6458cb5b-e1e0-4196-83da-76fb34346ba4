<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Document</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        #canvas-container {
            width: 800px;
            height: 800px;
        }
    </style>
</head>

<body>
    <div class="canvas-container">
        <div id="canvas-container"></div>
    </div>

    <script>
        let renderer, scene, camera;

        scene = new THREE.Scene()
        scene.background = new THREE.Color(0xf0f0f0)

        camera = new THREE.PerspectiveCamera(75, 800 / 800, 0.1, 1000)
        camera.position.set(0, 0, 5)

        renderer = new THREE.WebGLRenderer({ antialias: true })

        const container = document.getElementById('canvas-container');
        renderer.setSize(800, 800)
        container.appendChild(renderer.domElement)

        // 几何体
        const geometry = new THREE.ConeGeometry(1, 2, 32) // 圆锥体
        // 材质
        const material = new THREE.MeshStandardMaterial({
            color: 0x0ff000,
            wireframe: false,
            metalness: 0.5,
            roughness: 0.2
        })

        // 环境光
        const ambientLight = new THREE.AmbientLight(0xffff00, 0.3)
        scene.add(ambientLight)

        // 方向光
        const directLight = new THREE.DirectionalLight(0xffffff, 0.8)
        directLight.position.set(2, -5, 5)
        scene.add(directLight)

        // 添加光源辅助器
        const lightHelper = new THREE.DirectionalLightHelper(directLight, 1, 0x0000ff);
        scene.add(lightHelper);

        const mesh = new THREE.Mesh(geometry, material)
        scene.add(mesh)

        // 旋转物体以更好地观察
        mesh.rotation.x = -Math.PI / 3;
        mesh.rotation.y = -Math.PI / 3;


        function animate() {
            requestAnimationFrame(animate);


            // 旋转物体以更好地观察
            mesh.rotation.x -= 0.01
            mesh.rotation.y -= 0.01

            // 渲染场景
            renderer.render(scene, camera);
        }

        animate();

    </script>

</body>

</html>