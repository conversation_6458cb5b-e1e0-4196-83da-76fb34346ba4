/**
 * Three.js Demo 2: 几何体和材质
 * 展示不同的几何体和材质类型，学习Three.js的基础对象
 *
 * 重点学习：
 * 1. 各种几何体的创建和参数
 * 2. 不同材质的特性和用途
 * 3. 材质与光照的关系
 */

// 全局变量
let scene, camera, renderer;
let currentMesh; // 当前显示的3D对象
let light; // 光源（某些材质需要光照）
let animationId;

// 控制参数
let rotationSpeed = 0.01;
let isRotating = true;
let isWireframe = false;
let currentColor = 0x3498db;

/**
 * 初始化场景
 */
function init() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(0, 0, 5);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });

  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();
  renderer.setSize(containerRect.width, containerRect.height);
  container.appendChild(renderer.domElement);

  // 添加光源（某些材质需要光照才能看到效果）
  addLights();

  // 创建默认对象（立方体 + 基础材质）
  createObject("box", "basic");

  // 设置控制器
  setupControls();

  // 开始动画循环
  animate();
}

/**
 * 添加光源
 * 为了让不同材质都能正确显示，我们添加多种光源
 */
function addLights() {
  // 环境光：提供整体的基础照明，让物体不会完全黑暗
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
  scene.add(ambientLight);

  // 方向光：模拟太阳光，提供主要照明
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(5, 5, 5);
  scene.add(directionalLight);

  // 点光源：提供局部照明，增加立体感
  const pointLight = new THREE.PointLight(0xffffff, 0.5);
  pointLight.position.set(0,0,5);
  scene.add(pointLight);

  // 添加光源辅助器
  const lightHelper = new THREE.DirectionalLightHelper(
    directionalLight,
    10,
    0x0000ff
  );
  scene.add(lightHelper);
  const lightHelper2 = new THREE.PointLightHelper(pointLight, 10, 0x00f0f0);
  scene.add(lightHelper2);
}

/**
 * 创建3D对象
 * @param {string} geometryType - 几何体类型
 * @param {string} materialType - 材质类型
 */
function createObject(geometryType, materialType) {
  // 如果已有对象，先从场景中移除
  if (currentMesh) {
    scene.remove(currentMesh);
    // 释放几何体和材质的内存
    currentMesh.geometry.dispose();
    currentMesh.material.dispose();
  }

  // 创建几何体
  const geometry = createGeometry(geometryType);

  // 创建材质
  const material = createMaterial(materialType);

  // 创建网格对象
  currentMesh = new THREE.Mesh(geometry, material);

  // 添加到场景
  scene.add(currentMesh);
}

/**
 * 创建几何体
 * @param {string} type - 几何体类型
 * @returns {THREE.Geometry} 几何体对象
 */
function createGeometry(type) {
  switch (type) {
    case "box":
      // 立方体：宽度、高度、深度
      return new THREE.BoxGeometry(1, 1, 1);

    case "sphere":
      // 球体：半径、水平分段数、垂直分段数
      return new THREE.SphereGeometry(1, 32, 32);

    case "cylinder":
      // 圆柱体：顶部半径、底部半径、高度、径向分段数
      return new THREE.CylinderGeometry(1, 1, 2, 32);

    case "cone":
      // 圆锥体：底部半径、高度、径向分段数
      return new THREE.ConeGeometry(1, 2, 32);

    case "torus":
      // 圆环体：环半径、管半径、径向分段数、管状分段数
      return new THREE.TorusGeometry(1, 0.4, 16, 100);

    case "plane":
      // 平面：宽度、高度
      return new THREE.PlaneGeometry(2, 2);

    default:
      return new THREE.BoxGeometry(1, 1, 1);
  }
}

/**
 * 创建材质
 * @param {string} type - 材质类型
 * @returns {THREE.Material} 材质对象
 */
function createMaterial(type) {
  const materialOptions = {
    color: currentColor,
    wireframe: isWireframe,
  };

  switch (type) {
    case "basic":
      // 基础材质：不受光照影响，颜色恒定
      // 适用于：UI元素、发光物体、简单的装饰对象
      return new THREE.MeshBasicMaterial(materialOptions);

    case "lambert":
      // 朗伯材质：只有漫反射，没有镜面反射
      // 适用于：粗糙表面，如木材、石头、布料
      return new THREE.MeshLambertMaterial(materialOptions);

    case "phong":
      // 冯氏材质：支持镜面反射，可以产生高光
      // 适用于：有一定光泽的表面，如塑料、陶瓷
      return new THREE.MeshPhongMaterial({
        ...materialOptions,
        shininess: 100, // 光泽度
      });

    case "standard":
      // 标准材质：基于物理的渲染(PBR)，最真实
      // 适用于：需要真实感的场景，如金属、玻璃
      return new THREE.MeshStandardMaterial({
        ...materialOptions,
        metalness: 0.3, // 金属度
        roughness: 0.4, // 粗糙度
      });

    default:
      return new THREE.MeshBasicMaterial(materialOptions);
  }
}

/**
 * 动画循环
 */
function animate() {
  animationId = requestAnimationFrame(animate);

  // 旋转对象
  if (isRotating && currentMesh) {
    currentMesh.rotation.x += rotationSpeed;
    currentMesh.rotation.y += rotationSpeed;
  }

  // 渲染场景
  renderer.render(scene, camera);
}

/**
 * 设置控制器
 */
function setupControls() {
  // 几何体类型选择
  const geometrySelect = document.getElementById("geometryType");
  geometrySelect.addEventListener("change", function (event) {
    const materialType = document.getElementById("materialType").value;
    createObject(event.target.value, materialType);
  });

  // 材质类型选择
  const materialSelect = document.getElementById("materialType");
  materialSelect.addEventListener("change", function (event) {
    const geometryType = document.getElementById("geometryType").value;
    createObject(geometryType, event.target.value);
  });

  // 颜色选择
  const colorPicker = document.getElementById("objectColor");
  colorPicker.addEventListener("input", function (event) {
    currentColor = parseInt(event.target.value.replace("#", "0x"));
    // 更新当前对象的颜色
    if (currentMesh) {
      currentMesh.material.color.setHex(currentColor);
    }
  });

  // 线框模式切换
  const wireframeButton = document.getElementById("wireframeButton");
  wireframeButton.addEventListener("click", function () {
    isWireframe = !isWireframe;
    if (currentMesh) {
      currentMesh.material.wireframe = isWireframe;
    }
    wireframeButton.classList.toggle("active", isWireframe);
    wireframeButton.textContent = isWireframe ? "🎨 实体模式" : "📐 线框模式";
  });

  // 旋转控制
  const rotationButton = document.getElementById("rotationButton");
  rotationButton.addEventListener("click", function () {
    isRotating = !isRotating;
    rotationButton.classList.toggle("active", isRotating);
    rotationButton.textContent = isRotating ? "⏸️ 停止旋转" : "🔄 自动旋转";
  });

  // 旋转速度控制
  const rotationSpeedSlider = document.getElementById("rotationSpeed");
  const rotationSpeedValue = document.getElementById("rotationSpeed-value");

  rotationSpeedSlider.addEventListener("input", function (event) {
    rotationSpeed = parseFloat(event.target.value);
    rotationSpeedValue.textContent = rotationSpeed.toFixed(3);
  });
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();

  camera.aspect = containerRect.width / containerRect.height;
  camera.updateProjectionMatrix();

  renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener("resize", onWindowResize);
window.addEventListener("load", init);
