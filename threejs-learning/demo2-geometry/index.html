<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 2: 几何体和材质</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }

        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .controls {
            width: 380px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            background: white;
            margin-bottom: 10px;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }

        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }

        .control-group button:hover {
            opacity: 0.9;
        }

        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }

        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }

        .section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>

<body>
    <a href="../index.html" class="back-button">← 返回主页</a>

    <div class="header">
        <h1>🔷 几何体和材质</h1>
        <p>探索Three.js的各种几何体和材质类型</p>
    </div>

    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>

        <div class="controls">
            <h3>🎨 对象控制</h3>

            <div class="section">
                <h4>📐 几何体类型</h4>
                <div class="control-group">
                    <label for="geometryType">选择几何体</label>
                    <select id="geometryType">
                        <option value="box">立方体 (BoxGeometry)</option>
                        <option value="sphere">球体 (SphereGeometry)</option>
                        <option value="cylinder">圆柱体 (CylinderGeometry)</option>
                        <option value="cone">圆锥体 (ConeGeometry)</option>
                        <option value="torus">圆环体 (TorusGeometry)</option>
                        <option value="plane">平面 (PlaneGeometry)</option>
                    </select>
                </div>
            </div>

            <div class="section">
                <h4>🎨 材质类型</h4>
                <div class="control-group">
                    <label for="materialType">选择材质</label>
                    <select id="materialType">
                        <option value="basic">基础材质 (MeshBasicMaterial)</option>
                        <option value="lambert">朗伯材质 (MeshLambertMaterial)</option>
                        <option value="phong">冯氏材质 (MeshPhongMaterial)</option>
                        <option value="standard">标准材质 (MeshStandardMaterial)</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="objectColor">物体颜色</label>
                    <input type="color" id="objectColor" value="#3498db">
                </div>
            </div>

            <div class="section">
                <h4>⚙️ 显示选项</h4>
                <div class="control-group">
                    <button id="wireframeButton">📐 线框模式</button>
                    <button id="rotationButton" class="active">🔄 自动旋转</button>
                </div>
            </div>

            <div class="control-group">
                <label for="rotationSpeed">旋转速度</label>
                <input type="range" id="rotationSpeed" min="0" max="0.05" step="0.005" value="0.01">
                <div class="value-display" id="rotationSpeed-value">0.01</div>
            </div>

            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>几何体(Geometry)</strong>：定义3D对象的形状和结构</p>
                <p><strong>材质(Material)</strong>：定义表面的外观和光照响应</p>
                <p><strong>基础材质</strong>：不受光照影响，颜色恒定</p>
                <p><strong>朗伯材质</strong>：漫反射，适合粗糙表面</p>
                <p><strong>冯氏材质</strong>：支持镜面反射，有光泽效果</p>
                <p><strong>标准材质</strong>：基于物理的渲染，最真实</p>
            </div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="geometry-materials.js"></script>
</body>

</html>