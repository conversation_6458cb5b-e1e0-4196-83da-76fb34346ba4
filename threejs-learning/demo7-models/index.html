<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 7: 模型加载与骨骼动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .controls {
            width: 380px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            background: white;
            margin-bottom: 10px;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .model-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .model-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .loading-status {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .animation-list {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        
        .animation-item {
            padding: 5px;
            cursor: pointer;
            border-radius: 3px;
            margin-bottom: 2px;
        }
        
        .animation-item:hover {
            background: #f0f0f0;
        }
        
        .animation-item.active {
            background: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🎭 模型加载与骨骼动画</h1>
        <p>学习3D模型导入和角色动画系统</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <h3>🎭 模型控制</h3>
            
            <div class="model-section">
                <h4>📦 模型选择</h4>
                <div class="control-group">
                    <label for="modelType">选择模型</label>
                    <select id="modelType">
                        <option value="robot">机器人 (带动画)</option>
                        <option value="character">角色 (多动画)</option>
                        <option value="simple">简单模型</option>
                    </select>
                </div>
                
                <div class="loading-status" id="loadingStatus">
                    准备加载模型...
                </div>
            </div>
            
            <div class="model-section">
                <h4>🎬 动画控制</h4>
                <div class="control-group">
                    <label>可用动画</label>
                    <div class="animation-list" id="animationList">
                        <div class="animation-item">无动画数据</div>
                    </div>
                </div>
                
                <div class="control-group">
                    <label for="animationSpeed">动画速度</label>
                    <input type="range" id="animationSpeed" min="0" max="3" step="0.1" value="1">
                    <div class="value-display" id="animationSpeed-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <button id="playPauseButton">⏸️ 暂停动画</button>
                    <button id="resetAnimationButton">🔄 重置动画</button>
                </div>
            </div>
            
            <div class="model-section">
                <h4>🎯 相机控制</h4>
                <div class="control-group">
                    <label for="cameraDistance">相机距离</label>
                    <input type="range" id="cameraDistance" min="2" max="20" step="0.5" value="5">
                    <div class="value-display" id="cameraDistance-value">5.0</div>
                </div>
                
                <div class="control-group">
                    <button id="autoRotateButton">🔄 自动旋转</button>
                </div>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p><strong>GLTF格式</strong>：现代3D模型的标准格式</p>
                <p><strong>骨骼动画</strong>：通过骨骼控制模型变形</p>
                <p><strong>动画混合器</strong>：管理多个动画的播放</p>
                <p><strong>动画剪辑</strong>：独立的动画片段</p>
                <p><strong>关键帧插值</strong>：平滑的动画过渡</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://unpkg.com/three@0.128.0/build/three.min.js"></script>
    <script src="model-animation.js"></script>
</body>
</html>
