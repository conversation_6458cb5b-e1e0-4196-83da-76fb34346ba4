/**
 * Three.js Demo 7: 模型加载与骨骼动画
 * 学习3D模型导入和角色动画系统
 * 
 * 重点学习：
 * 1. GLTF模型加载器的使用
 * 2. 骨骼动画系统
 * 3. 动画混合器(AnimationMixer)
 * 4. 动画剪辑的控制
 */

// 全局变量
let scene, camera, renderer;
let mixer;              // 动画混合器
let currentModel;       // 当前加载的模型
let currentAction;      // 当前播放的动画
let animations = [];    // 可用的动画列表
let clock;              // 时钟，用于动画更新
let animationId;

// 控制参数
let isAnimationPlaying = true;
let animationSpeed = 1.0;
let autoRotate = false;

/**
 * 初始化场景
 */
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    
    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 2, 5);
    
    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    renderer.setSize(containerRect.width, containerRect.height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(renderer.domElement);
    
    // 创建时钟
    clock = new THREE.Clock();
    
    // 添加光源
    addLights();
    
    // 创建地面
    createGround();
    
    // 加载默认模型
    loadModel('robot');
    
    // 设置控制器
    setupControls();
    
    // 开始动画循环
    animate();
}

/**
 * 添加光源
 */
function addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);
    
    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 1024;
    directionalLight.shadow.mapSize.height = 1024;
    scene.add(directionalLight);
    
    // 补充光源
    const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
    fillLight.position.set(-5, 5, -5);
    scene.add(fillLight);
}

/**
 * 创建地面
 */
function createGround() {
    const geometry = new THREE.PlaneGeometry(20, 20);
    const material = new THREE.MeshLambertMaterial({ 
        color: 0x808080,
        transparent: true,
        opacity: 0.8
    });
    const ground = new THREE.Mesh(geometry, material);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);
}

/**
 * 加载3D模型
 * 注意：由于没有实际的模型文件，这里创建程序化的模型来演示概念
 */
function loadModel(modelType) {
    updateLoadingStatus('正在加载模型...');
    
    // 清除之前的模型
    if (currentModel) {
        scene.remove(currentModel);
    }
    
    // 重置动画系统
    if (mixer) {
        mixer.stopAllAction();
    }
    
    // 根据类型创建不同的模型
    switch (modelType) {
        case 'robot':
            createRobotModel();
            break;
        case 'character':
            createCharacterModel();
            break;
        case 'simple':
            createSimpleModel();
            break;
    }
}

/**
 * 创建机器人模型（程序化生成，模拟GLTF加载）
 */
function createRobotModel() {
    const group = new THREE.Group();
    
    // 身体
    const bodyGeometry = new THREE.BoxGeometry(1, 1.5, 0.5);
    const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0x4a90e2 });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 1;
    body.castShadow = true;
    body.userData.originalPosition = { x: 0, y: 1, z: 0 };
    group.add(body);

    // 头部
    const headGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
    const headMaterial = new THREE.MeshStandardMaterial({ color: 0x7ed321 });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 2.2;
    head.castShadow = true;
    head.userData.originalPosition = { x: 0, y: 2.2, z: 0 };
    group.add(head);
    
    // 手臂
    const armGeometry = new THREE.BoxGeometry(0.3, 1, 0.3);
    const armMaterial = new THREE.MeshStandardMaterial({ color: 0xf5a623 });

    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.8, 1, 0);
    leftArm.castShadow = true;
    leftArm.userData.originalPosition = { x: -0.8, y: 1, z: 0 };
    group.add(leftArm);

    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    rightArm.position.set(0.8, 1, 0);
    rightArm.castShadow = true;
    rightArm.userData.originalPosition = { x: 0.8, y: 1, z: 0 };
    group.add(rightArm);

    // 腿部
    const legGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.4);
    const legMaterial = new THREE.MeshStandardMaterial({ color: 0xd0021b });

    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.3, -0.1, 0);
    leftLeg.castShadow = true;
    leftLeg.userData.originalPosition = { x: -0.3, y: -0.1, z: 0 };
    group.add(leftLeg);

    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    rightLeg.position.set(0.3, -0.1, 0);
    rightLeg.castShadow = true;
    rightLeg.userData.originalPosition = { x: 0.3, y: -0.1, z: 0 };
    group.add(rightLeg);
    
    // 存储身体部位引用，用于动画
    group.userData = {
        body: body,
        head: head,
        leftArm: leftArm,
        rightArm: rightArm,
        leftLeg: leftLeg,
        rightLeg: rightLeg
    };
    
    currentModel = group;
    scene.add(group);
    
    // 创建动画
    createRobotAnimations();
    
    updateLoadingStatus('机器人模型加载完成');
}

/**
 * 创建角色模型
 */
function createCharacterModel() {
    const group = new THREE.Group();
    
    // 简化的人形模型
    const bodyGeometry = new THREE.CylinderGeometry(0.5, 0.5, 1.5, 8);
    const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0x8e44ad });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 1;
    body.castShadow = true;
    group.add(body);
    
    // 头部
    const headGeometry = new THREE.SphereGeometry(0.4, 16, 16);
    const headMaterial = new THREE.MeshStandardMaterial({ color: 0xf39c12 });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 2.2;
    head.castShadow = true;
    // 保存原始位置
    head.userData.originalPosition = { x: 0, y: 2.2, z: 0 };
    group.add(head);

    // 也为身体保存原始位置
    body.userData.originalPosition = { x: 0, y: 1, z: 0 };

    group.userData = { body: body, head: head };
    
    currentModel = group;
    scene.add(group);
    
    createCharacterAnimations();
    updateLoadingStatus('角色模型加载完成');
}

/**
 * 创建简单模型
 */
function createSimpleModel() {
    const geometry = new THREE.TorusKnotGeometry(1, 0.3, 100, 16);
    const material = new THREE.MeshStandardMaterial({ 
        color: 0xe74c3c,
        metalness: 0.3,
        roughness: 0.4
    });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.y = 1;
    mesh.castShadow = true;
    
    currentModel = mesh;
    scene.add(mesh);
    
    createSimpleAnimations();
    updateLoadingStatus('简单模型加载完成');
}

/**
 * 创建机器人动画
 */
function createRobotAnimations() {
    animations = [
        { name: '待机', action: null },
        { name: '挥手', action: null },
        { name: '行走', action: null },
        { name: '跳跃', action: null }
    ];
    
    // 创建动画混合器
    mixer = new THREE.AnimationMixer(currentModel);
    
    updateAnimationList();
}

/**
 * 创建角色动画
 */
function createCharacterAnimations() {
    animations = [
        { name: '呼吸', action: null },
        { name: '点头', action: null },
        { name: '旋转', action: null }
    ];
    
    mixer = new THREE.AnimationMixer(currentModel);
    updateAnimationList();
}

/**
 * 创建简单动画
 */
function createSimpleAnimations() {
    animations = [
        { name: '旋转', action: null },
        { name: '缩放', action: null }
    ];
    
    mixer = new THREE.AnimationMixer(currentModel);
    updateAnimationList();
}

/**
 * 重置模型变换状态
 */
function resetModelTransform() {
    if (!currentModel) return;

    // 清除挥手动画标记
    currentModel.userData.isWaving = false;

    // 重置位置、旋转、缩放
    currentModel.position.set(0, 0, 0);
    currentModel.rotation.set(0, 0, 0);
    currentModel.scale.set(1, 1, 1);

    // 递归重置所有子对象
    currentModel.traverse((child) => {
        if (child !== currentModel && child.userData && child.userData.originalPosition) {
            // 恢复到原始位置
            const originalPos = child.userData.originalPosition;
            child.position.set(originalPos.x, originalPos.y, originalPos.z);
            child.rotation.set(0, 0, 0);
            child.scale.set(1, 1, 1);
        }
    });
}

/**
 * 播放指定动画
 */
function playAnimation(animationName) {
    if (!mixer || !currentModel) return;

    // 停止当前动画
    if (currentAction) {
        currentAction.stop();
    }

    // 重置模型状态，防止动画之间的干扰
    resetModelTransform();

    // 根据动画名称创建相应的动画
    switch (animationName) {
        case '挥手':
            createWaveAnimation();
            break;
        case '行走':
            createWalkAnimation();
            break;
        case '跳跃':
            createJumpAnimation();
            break;
        case '呼吸':
            createBreathingAnimation();
            break;
        case '点头':
            createNodAnimation();
            break;
        case '旋转':
            createRotationAnimation();
            break;
        case '缩放':
            createScaleAnimation();
            break;
        default:
            currentAction = null;
            return;
    }
    
    if (currentAction) {
        currentAction.play();
    }
}

/**
 * 创建挥手动画
 */
function createWaveAnimation() {
    if (!currentModel.userData.rightArm) return;

    // 创建一个简单的动画，直接作用于整个模型组
    // 但我们会在animate函数中手动控制右臂的旋转
    const times = [0, 1.0];
    const rotationValues = [0, 0]; // 虚拟值，实际旋转在animate中处理

    const rotationTrack = new THREE.NumberKeyframeTrack(
        '.rotation[y]', // 作用于整个模型的y轴旋转（虚拟）
        times,
        rotationValues
    );

    const clip = new THREE.AnimationClip('wave', 1.0, [rotationTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopRepeat);

    // 标记这是挥手动画，在animate函数中会特殊处理
    currentModel.userData.isWaving = true;
    currentModel.userData.waveStartTime = Date.now();
}

/**
 * 创建行走动画
 */
function createWalkAnimation() {
    const times = [0, 0.25, 0.5, 0.75, 1.0];

    // 简单的左右摆动模拟行走
    const rotationValues = [0, Math.PI/12, 0, -Math.PI/12, 0];
    const rotationTrack = new THREE.NumberKeyframeTrack(
        '.rotation[z]',
        times,
        rotationValues
    );

    // 轻微的上下移动
    const positionValues = [0, 0.1, 0, 0.1, 0];
    const positionTrack = new THREE.NumberKeyframeTrack(
        '.position[y]',
        times,
        positionValues
    );

    const clip = new THREE.AnimationClip('walk', 1.0, [rotationTrack, positionTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopRepeat);
}

/**
 * 创建跳跃动画
 */
function createJumpAnimation() {
    const times = [0, 0.3, 0.6, 1.0];
    const positionValues = [0, 0, 2, 0];

    const positionTrack = new THREE.NumberKeyframeTrack(
        '.position[y]',
        times,
        positionValues
    );

    const clip = new THREE.AnimationClip('jump', 1.0, [positionTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopOnce);
}

/**
 * 创建呼吸动画
 */
function createBreathingAnimation() {
    const times = [0, 1.0, 2.0];
    // 每个时间点需要3个值 (x, y, z)
    const scaleValues = [
        1, 1, 1,      // t=0: 正常大小
        1.05, 1.05, 1.05,  // t=1: 稍微放大
        1, 1, 1       // t=2: 回到正常大小
    ];

    const scaleTrack = new THREE.VectorKeyframeTrack(
        '.scale',
        times,
        scaleValues
    );

    const clip = new THREE.AnimationClip('breathing', 2.0, [scaleTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopRepeat);
}

/**
 * 创建点头动画
 */
function createNodAnimation() {
    const times = [0, 0.5, 1.0];
    const rotationValues = [0, -Math.PI/8, 0];

    const rotationTrack = new THREE.NumberKeyframeTrack(
        '.rotation[x]',
        times,
        rotationValues
    );

    const clip = new THREE.AnimationClip('nod', 1.0, [rotationTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopRepeat);
}

/**
 * 创建旋转动画
 */
function createRotationAnimation() {
    const times = [0, 2.0];
    const rotationValues = [0, Math.PI * 2];

    const rotationTrack = new THREE.NumberKeyframeTrack(
        '.rotation[y]',
        times,
        rotationValues
    );

    const clip = new THREE.AnimationClip('rotation', 2.0, [rotationTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopRepeat);
}

/**
 * 创建缩放动画
 */
function createScaleAnimation() {
    const times = [0, 1.0, 2.0];
    const scaleValues = [1, 1, 1, 1.5, 1.5, 1.5, 1, 1, 1];

    const scaleTrack = new THREE.VectorKeyframeTrack(
        '.scale',
        times,
        scaleValues
    );

    const clip = new THREE.AnimationClip('scale', 2.0, [scaleTrack]);
    currentAction = mixer.clipAction(clip);
    currentAction.setLoop(THREE.LoopRepeat);
}

/**
 * 更新动画列表显示
 */
function updateAnimationList() {
    const animationList = document.getElementById('animationList');
    animationList.innerHTML = '';
    
    animations.forEach((anim, index) => {
        const item = document.createElement('div');
        item.className = 'animation-item';
        item.textContent = anim.name;
        item.addEventListener('click', () => {
            // 移除其他项的active类
            document.querySelectorAll('.animation-item').forEach(el => {
                el.classList.remove('active');
            });
            // 添加active类到当前项
            item.classList.add('active');
            // 播放动画
            playAnimation(anim.name);
        });
        animationList.appendChild(item);
    });
}

/**
 * 更新加载状态
 */
function updateLoadingStatus(message) {
    document.getElementById('loadingStatus').textContent = message;
}

/**
 * 动画循环
 */
function animate() {
    animationId = requestAnimationFrame(animate);
    
    const deltaTime = clock.getDelta();
    
    // 更新动画混合器
    if (mixer && isAnimationPlaying) {
        mixer.update(deltaTime * animationSpeed);
    }

    // 处理挥手动画
    if (currentModel && currentModel.userData.isWaving && currentModel.userData.rightArm) {
        const elapsedTime = (Date.now() - currentModel.userData.waveStartTime) / 1000;
        const waveFrequency = 2; // 每秒2次挥手
        const waveAmplitude = Math.PI / 3; // 60度摆动

        // 使用正弦波创建挥手动作
        const waveRotation = Math.sin(elapsedTime * waveFrequency * Math.PI * 2) * waveAmplitude;
        currentModel.userData.rightArm.rotation.z = waveRotation;
    }

    // 自动旋转相机
    if (autoRotate) {
        const time = clock.getElapsedTime();
        camera.position.x = Math.cos(time * 0.5) * 5;
        camera.position.z = Math.sin(time * 0.5) * 5;
        camera.lookAt(0, 1, 0);
    }
    
    // 渲染场景
    renderer.render(scene, camera);
}

/**
 * 设置控制器
 */
function setupControls() {
    // 模型类型选择
    const modelSelect = document.getElementById('modelType');
    modelSelect.addEventListener('change', function(event) {
        loadModel(event.target.value);
    });
    
    // 动画速度控制
    const speedSlider = document.getElementById('animationSpeed');
    const speedValue = document.getElementById('animationSpeed-value');
    
    speedSlider.addEventListener('input', function(event) {
        animationSpeed = parseFloat(event.target.value);
        speedValue.textContent = `${animationSpeed}x`;
    });
    
    // 播放/暂停按钮
    const playPauseButton = document.getElementById('playPauseButton');
    playPauseButton.addEventListener('click', function() {
        isAnimationPlaying = !isAnimationPlaying;
        playPauseButton.textContent = isAnimationPlaying ? '⏸️ 暂停动画' : '▶️ 播放动画';
        playPauseButton.classList.toggle('active', !isAnimationPlaying);
    });
    
    // 重置动画按钮
    const resetButton = document.getElementById('resetAnimationButton');
    resetButton.addEventListener('click', function() {
        if (currentAction) {
            currentAction.reset();
            currentAction.play();
        }
    });
    
    // 相机距离控制
    const distanceSlider = document.getElementById('cameraDistance');
    const distanceValue = document.getElementById('cameraDistance-value');
    
    distanceSlider.addEventListener('input', function(event) {
        const distance = parseFloat(event.target.value);
        camera.position.normalize().multiplyScalar(distance);
        distanceValue.textContent = distance.toFixed(1);
    });
    
    // 自动旋转按钮
    const autoRotateButton = document.getElementById('autoRotateButton');
    autoRotateButton.addEventListener('click', function() {
        autoRotate = !autoRotate;
        autoRotateButton.classList.toggle('active', autoRotate);
        autoRotateButton.textContent = autoRotate ? '⏸️ 停止旋转' : '🔄 自动旋转';
        
        if (!autoRotate) {
            // 重置相机位置
            camera.position.set(0, 2, 5);
            camera.lookAt(0, 1, 0);
        }
    });
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
    const container = document.getElementById('canvas-container');
    const containerRect = container.getBoundingClientRect();
    
    camera.aspect = containerRect.width / containerRect.height;
    camera.updateProjectionMatrix();
    
    renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener('resize', onWindowResize);
window.addEventListener('load', init);
