/**
 * Three.js Demo 1: 基础场景
 * 学习Three.js的核心概念：场景、相机、渲染器
 *
 * 这个demo展示了Three.js最基础的概念，代码简洁易懂，避免过度封装
 */

// 全局变量 - 保持简单，便于理解
let scene; // 场景：3D世界的容器
let camera; // 相机：观察者的视角
let renderer; // 渲染器：将3D场景绘制到屏幕
let cube; // 立方体：我们的3D对象
let animationId; // 动画ID：用于控制动画

// 控制参数
let rotationSpeed = 0.01; // 旋转速度
let isPaused = false; // 是否暂停

/**
 * 初始化Three.js场景
 * 这是Three.js的标准初始化流程
 */
function init() {
  // 1. 创建场景
  // Scene是3D世界的容器，所有的3D对象都要添加到场景中
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0); // 设置背景颜色为浅灰色

  // 2. 创建相机
  // PerspectiveCamera创建透视相机，模拟人眼的视觉效果
  // 参数：视野角度(75度), 宽高比, 近裁剪面(0.1), 远裁剪面(1000)
  camera = new THREE.PerspectiveCamera(
    75, // 视野角度
    window.innerWidth / window.innerHeight, // 宽高比
    0.1, // 近裁剪面
    1000 // 远裁剪面
  );

  // 设置相机位置 (x, y, z)
  camera.position.set(0, 0, 5);

  // 3. 创建渲染器
  // WebGLRenderer使用WebGL进行硬件加速渲染
  renderer = new THREE.WebGLRenderer({
    antialias: true, // 开启抗锯齿，让边缘更平滑
  });

  // 获取容器并设置渲染器大小
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();
  renderer.setSize(containerRect.width, containerRect.height);

  // 将渲染器的canvas元素添加到页面
  container.appendChild(renderer.domElement);

  // 4. 创建3D对象
  createCube();

  // 5. 设置控制器
  setupControls();

  // 6. 开始渲染循环
  animate();
}

/**
 * 创建立方体
 * 在Three.js中，3D对象由几何体(Geometry)和材质(Material)组成
 */
function createCube() {
  // 创建立方体几何体
  // BoxGeometry创建一个立方体的几何形状
  // 参数：宽度(1), 高度(1), 深度(1)
  const geometry = new THREE.BoxGeometry(1, 1, 1);

  // 创建材质
  // MeshBasicMaterial是最简单的材质，不受光照影响
  const material = new THREE.MeshBasicMaterial({
    color: 0x3498db, // 蓝色
    wireframe: true, // 不显示线框
  });

  // 创建网格对象
  // Mesh = Geometry + Material，这是Three.js中可见3D对象的基本单位
  cube = new THREE.Mesh(geometry, material);

  // 将立方体添加到场景中
  scene.add(cube);
}

/**
 * 动画循环函数
 * 这是Three.js应用的核心，负责更新和渲染场景
 */
function animate() {
  // 请求下一帧动画
  // requestAnimationFrame确保动画与浏览器刷新率同步
  animationId = requestAnimationFrame(animate);

  // 更新立方体旋转
  if (!isPaused) {
    // 绕X轴和Y轴旋转，创建有趣的旋转效果
    cube.rotation.x += rotationSpeed;
    cube.rotation.y += rotationSpeed;
  }

  // 渲染场景
  // 这一步将3D场景绘制到2D屏幕上
  renderer.render(scene, camera);
}

/**
 * 设置控制器
 * 处理用户界面的交互
 */
function setupControls() {
  // 旋转速度控制
  const rotationSpeedSlider = document.getElementById("rotationSpeed");
  const rotationSpeedValue = document.getElementById("rotationSpeed-value");

  rotationSpeedSlider.addEventListener("input", function (event) {
    rotationSpeed = parseFloat(event.target.value);
    rotationSpeedValue.textContent = rotationSpeed.toFixed(3);
  });

  // 相机距离控制
  const cameraDistanceSlider = document.getElementById("cameraDistance");
  const cameraDistanceValue = document.getElementById("cameraDistance-value");

  cameraDistanceSlider.addEventListener("input", function (event) {
    const distance = parseFloat(event.target.value);
    // 更新相机Z轴位置
    camera.position.z = distance;
    cameraDistanceValue.textContent = distance.toString();
  });

  // 暂停/继续按钮
  const pauseButton = document.getElementById("pauseButton");
  pauseButton.addEventListener("click", function () {
    isPaused = !isPaused;
    // 更新按钮文本和样式
    if (isPaused) {
      pauseButton.textContent = "▶️ 继续旋转";
      pauseButton.classList.add("active");
    } else {
      pauseButton.textContent = "⏸️ 暂停旋转";
      pauseButton.classList.remove("active");
    }
  });

  // 重置按钮
  const resetButton = document.getElementById("resetButton");
  resetButton.addEventListener("click", function () {
    // 重置立方体旋转
    cube.rotation.x = 0;
    cube.rotation.y = 0;

    // 重置相机位置
    camera.position.set(0, 0, 5);

    // 重置控制参数
    rotationSpeed = 0.01;
    isPaused = false;

    // 重置UI
    rotationSpeedSlider.value = 0.01;
    rotationSpeedValue.textContent = "0.01";
    cameraDistanceSlider.value = 5;
    cameraDistanceValue.textContent = "5";
    pauseButton.textContent = "⏸️ 暂停旋转";
    pauseButton.classList.remove("active");
  });
}

/**
 * 处理窗口大小变化
 * 当浏览器窗口大小改变时，需要更新相机和渲染器
 */
function onWindowResize() {
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();

  // 更新相机宽高比
  camera.aspect = containerRect.width / containerRect.height;
  camera.updateProjectionMatrix(); // 重要：更新投影矩阵

  // 更新渲染器大小
  renderer.setSize(containerRect.width, containerRect.height);
}

// 监听窗口大小变化
window.addEventListener("resize", onWindowResize);

// 页面加载完成后初始化
window.addEventListener("load", init);
