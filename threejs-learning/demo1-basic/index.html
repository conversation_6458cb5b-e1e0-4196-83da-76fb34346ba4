<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 1: 基础场景</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }

        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .controls {
            width: 350px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }

        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }

        .control-group button:hover {
            opacity: 0.9;
        }

        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }

        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }

        .code-section {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .code-section h4 {
            color: #3498db;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <a href="../index.html" class="back-button">← 返回主页</a>

    <div class="header">
        <h1>🎲 基础场景</h1>
        <p>学习Three.js的核心概念：场景、相机、渲染器</p>
    </div>

    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>

        <div class="controls">
            <h3>🎮 场景控制</h3>

            <div class="control-group">
                <label for="rotationSpeed">旋转速度</label>
                <input type="range" id="rotationSpeed" min="0" max="0.1" step="0.005" value="0.01">
                <div class="value-display" id="rotationSpeed-value">0.01</div>
            </div>

            <div class="control-group">
                <label for="cameraDistance">相机距离</label>
                <input type="range" id="cameraDistance" min="3" max="15" step="0.5" value="5">
                <div class="value-display" id="cameraDistance-value">5</div>
            </div>

            <div class="control-group">
                <button id="pauseButton">⏸️ 暂停旋转</button>
                <button id="resetButton">🔄 重置场景</button>
            </div>

            <div class="explanation">
                <h4>💡 核心概念</h4>
                <p><strong>场景(Scene)</strong>：3D世界的容器，所有物体都放在场景中</p>
                <p><strong>相机(Camera)</strong>：观察者的视角，决定我们看到什么</p>
                <p><strong>渲染器(Renderer)</strong>：将3D场景绘制到2D屏幕上</p>
                <p><strong>网格(Mesh)</strong>：几何体 + 材质 = 可见的3D对象</p>
            </div>

            <div class="code-section">
                <h4>🔧 基础代码结构</h4>
                <div>
                    // 1. 创建场景<br>
                    scene = new THREE.Scene();<br><br>
                    // 2. 创建相机<br>
                    camera = new THREE.PerspectiveCamera();<br><br>
                    // 3. 创建渲染器<br>
                    renderer = new THREE.WebGLRenderer();<br><br>
                    // 4. 创建物体<br>
                    mesh = new THREE.Mesh(geometry, material);<br><br>
                    // 5. 渲染循环<br>
                    function animate() {<br>
                    &nbsp;&nbsp;renderer.render(scene, camera);<br>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="basic-scene.js"></script>
</body>

</html>