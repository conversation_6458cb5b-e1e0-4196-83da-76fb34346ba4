# 🚀 Three.js 学习之旅

一个专为新手设计的Three.js学习项目，通过6个渐进式的交互demo，帮助你从零开始掌握Three.js 3D编程。

## 🎯 设计理念

- **代码简洁易懂**：避免过度封装，每行代码都有详细注释
- **功能单一专注**：每个demo专注一个核心概念，便于理解
- **API调用清晰**：直接使用Three.js原生API，思路清晰
- **渐进式学习**：从基础到高级，循序渐进

## 📚 学习路径

### 🎲 Demo 1: 基础场景
**核心概念**: Three.js三大组件
- 场景(Scene)：3D世界的容器
- 相机(Camera)：观察者的视角
- 渲染器(Renderer)：将3D绘制到2D屏幕
- 网格(Mesh)：几何体 + 材质

**学会什么**:
- Three.js的基本工作流程
- 创建第一个3D场景
- 动画循环的概念

### 🔷 Demo 2: 几何体和材质
**核心概念**: 3D对象的构成
- 各种几何体：立方体、球体、圆柱体等
- 材质类型：基础、朗伯、冯氏、标准材质
- 材质与光照的关系

**学会什么**:
- 如何创建不同形状的3D对象
- 材质如何影响物体外观
- 线框模式的切换

### 💡 Demo 3: 光照系统
**核心概念**: 真实的光照效果
- 环境光：均匀照明
- 方向光：平行光线（太阳光）
- 点光源：从一点发光（灯泡）
- 聚光灯：锥形光束（手电筒）

**学会什么**:
- 不同光源的特点和用途
- 如何调节光照参数
- 阴影的基本设置

### 🖼️ Demo 4: 纹理和贴图
**核心概念**: 为3D对象添加表面细节
- 程序化纹理生成
- UV坐标系统
- 纹理包装模式
- 纹理过滤方式

**学会什么**:
- 如何创建和应用纹理
- UV坐标的控制方法
- 纹理重复和偏移效果

### 🎬 Demo 5: 动画系统
**核心概念**: 让3D世界动起来
- requestAnimationFrame动画循环
- 基础变换动画
- 缓动函数的使用
- 多种动画效果组合

**学会什么**:
- 动画的基本原理
- 如何创建平滑的动画效果
- 动画速度和暂停控制

### 🎮 Demo 6: 交互控制
**核心概念**: 用户交互和场景管理
- 射线投射进行物体拾取
- 鼠标事件处理
- 键盘输入控制
- 相机交互控制

**学会什么**:
- 如何检测鼠标点击的物体
- 实现相机的交互控制
- 键盘控制物体移动

## 🚀 快速开始

1. **下载项目**
   ```bash
   # 如果是git仓库
   git clone <repository-url>
   cd threejs-learning
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 或使用任何静态文件服务器
   ```

3. **开始学习**
   - 打开浏览器访问 `http://localhost:8000`
   - 按顺序学习每个demo
   - 仔细阅读代码注释
   - 尝试修改参数观察效果

## 💡 学习建议

### 对于完全新手
1. **按顺序学习**：不要跳过任何demo，每个都有重要概念
2. **动手实践**：修改代码参数，观察效果变化
3. **理解概念**：重点理解Three.js的核心概念
4. **查看注释**：每行重要代码都有详细解释

### 对于有编程基础的学习者
1. **关注API**：学习Three.js的API设计思路
2. **扩展功能**：尝试添加新功能或效果
3. **性能优化**：思考如何优化渲染性能
4. **深入源码**：理解Three.js的内部实现

## 🔧 技术特点

- **无依赖**：只使用Three.js CDN，无需复杂构建
- **现代浏览器**：支持所有支持WebGL的现代浏览器
- **响应式设计**：适配不同屏幕尺寸
- **详细注释**：每个重要概念都有解释

## 📖 代码风格

```javascript
// ✅ 好的例子：清晰的变量命名和注释
// 创建场景 - 3D世界的容器
const scene = new THREE.Scene();

// 创建透视相机 - 模拟人眼视觉
const camera = new THREE.PerspectiveCamera(
    75,                                    // 视野角度
    window.innerWidth / window.innerHeight, // 宽高比
    0.1,                                   // 近裁剪面
    1000                                   // 远裁剪面
);

// ❌ 避免的例子：过度封装
class SceneManager {
    constructor() {
        this.initializeEverything();
    }
    // 新手难以理解内部发生了什么
}
```

## 🎓 学习成果

完成所有demo后，你将掌握：

- Three.js的核心概念和工作原理
- 3D场景的创建和管理
- 各种几何体和材质的使用
- 光照系统的配置
- 纹理和贴图技术
- 动画系统的实现
- 用户交互的处理

## 🔗 扩展学习

- [Three.js官方文档](https://threejs.org/docs/)
- [Three.js示例](https://threejs.org/examples/)
- [WebGL基础教程](https://webglfundamentals.org/)

## 📝 许可证

本项目采用 MIT 许可证，可自由使用和修改。

---

**开始你的Three.js学习之旅吧！** 🚀

记住：3D编程需要耐心和实践，不要急于求成，享受创造3D世界的乐趣！
