<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 学习之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .demo-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .demo-card p {
            color: #7f8c8d;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .difficulty {
            display: inline-block;
            padding: 6px 15px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .difficulty.beginner {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .difficulty.intermediate {
            background: #ffeaa7;
            color: #e17055;
        }
        
        .difficulty.advanced {
            background: #fab1a0;
            color: #e84393;
        }
        
        .demo-link {
            display: inline-block;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            transition: opacity 0.3s ease;
        }
        
        .demo-link:hover {
            opacity: 0.9;
        }
        
        .intro {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .intro h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .intro p {
            color: #7f8c8d;
            line-height: 1.8;
            margin-bottom: 15px;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .features {
            background: #ecf0f1;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .features h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            color: #7f8c8d;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .features li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="container">
        <h1>🚀 Three.js 学习之旅</h1>
        
        <div class="intro">
            <h2>欢迎来到Three.js的世界！</h2>
            <p>Three.js是一个强大的JavaScript 3D库，让Web 3D开发变得简单而有趣。这个学习路径包含6个基础demo和3个进阶demo，将带你从零开始，逐步掌握Three.js的核心概念和高级技术。</p>
            <p><strong>学习特色：</strong>代码简洁易懂，避免过度封装，每个demo专注单一功能，详细注释帮助理解。完成基础6个demo后，可以挑战进阶内容。</p>
        </div>
        
        <div class="features">
            <h3>🎯 你将学到什么</h3>
            <ul>
                <li><strong>基础篇：</strong>Three.js核心概念、几何体材质、光照系统</li>
                <li><strong>进阶篇：</strong>纹理贴图、动画系统、用户交互</li>
                <li><strong>高级篇：</strong>模型加载、后处理效果、物理引擎</li>
                <li>GLTF模型导入和骨骼动画</li>
                <li>屏幕空间效果和自定义着色器</li>
                <li>物理模拟和碰撞检测</li>
            </ul>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card" onclick="window.location.href='demo1-basic/index.html'">
                <div class="difficulty beginner">入门</div>
                <h3>01. 基础场景</h3>
                <p>学习Three.js的三大核心组件：场景(Scene)、相机(Camera)、渲染器(Renderer)。创建你的第一个3D场景。</p>
                <a href="demo1-basic/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demo2-geometry/index.html'">
                <div class="difficulty beginner">入门</div>
                <h3>02. 几何体和材质</h3>
                <p>探索Three.js提供的各种几何体和材质类型，理解3D对象的构成。</p>
                <a href="demo2-geometry/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demo3-lighting/index.html'">
                <div class="difficulty intermediate">进阶</div>
                <h3>03. 光照系统</h3>
                <p>学习不同类型的光源：环境光、方向光、点光源、聚光灯，创建真实的光照效果。</p>
                <a href="demo3-lighting/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demo4-textures/index.html'">
                <div class="difficulty intermediate">进阶</div>
                <h3>04. 纹理和贴图</h3>
                <p>掌握纹理加载、UV映射，学习法线贴图、环境贴图等高级纹理技术。</p>
                <a href="demo4-textures/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demo5-animation/index.html'">
                <div class="difficulty intermediate">进阶</div>
                <h3>05. 动画系统</h3>
                <p>学习Three.js的动画系统，包括关键帧动画、补间动画和动画混合器。</p>
                <a href="demo5-animation/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demo6-interaction/index.html'">
                <div class="difficulty advanced">高级</div>
                <h3>06. 交互控制</h3>
                <p>添加鼠标控制、键盘输入，实现物体拾取和用户交互功能。</p>
                <a href="demo6-interaction/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demo7-models/index.html'">
                <div class="difficulty advanced">高级</div>
                <h3>07. 模型加载与骨骼动画</h3>
                <p>学习GLTF模型加载、骨骼动画系统和动画混合器的使用。</p>
                <a href="demo7-models/index.html" class="demo-link">开始学习</a>
            </div>

            <div class="demo-card" onclick="window.location.href='demo8-postprocessing/index.html'">
                <div class="difficulty advanced">高级</div>
                <h3>08. 后处理效果管线</h3>
                <p>掌握屏幕空间效果处理、自定义着色器和渲染管线设计。</p>
                <a href="demo8-postprocessing/index.html" class="demo-link">开始学习</a>
            </div>

            <div class="demo-card" onclick="window.location.href='demo9-physics/index.html'">
                <div class="difficulty advanced">高级</div>
                <h3>09. 物理引擎集成</h3>
                <p>学习Cannon.js物理引擎集成，实现真实的物理模拟和碰撞检测。</p>
                <a href="demo9-physics/index.html" class="demo-link">开始学习</a>
            </div>
        </div>
    </div>
</body>
</html>
