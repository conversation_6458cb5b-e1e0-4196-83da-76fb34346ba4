<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Demo 3: 光照系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        #canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .controls {
            width: 400px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #7f8c8d;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }
        
        .value-display {
            color: #2c3e50;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #3498db;
        }
        
        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #7f8c8d;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .light-section {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        
        .light-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .light-toggle {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .light-toggle input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .light-toggle label {
            margin: 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>💡 光照系统</h1>
        <p>学习Three.js的各种光源类型和光照效果</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <div id="canvas-container"></div>
        </div>
        
        <div class="controls">
            <h3>💡 光照控制</h3>
            
            <div class="light-section">
                <h4>🌅 环境光 (AmbientLight)</h4>
                <div class="light-toggle">
                    <input type="checkbox" id="ambientToggle" checked>
                    <label for="ambientToggle">启用环境光</label>
                </div>
                <div class="control-group">
                    <label for="ambientIntensity">强度</label>
                    <input type="range" id="ambientIntensity" min="0" max="1" step="0.1" value="0.4">
                    <div class="value-display" id="ambientIntensity-value">0.4</div>
                </div>
                <div class="control-group">
                    <label for="ambientColor">颜色</label>
                    <input type="color" id="ambientColor" value="#404040">
                </div>
            </div>
            
            <div class="light-section">
                <h4>☀️ 方向光 (DirectionalLight)</h4>
                <div class="light-toggle">
                    <input type="checkbox" id="directionalToggle" checked>
                    <label for="directionalToggle">启用方向光</label>
                </div>
                <div class="control-group">
                    <label for="directionalIntensity">强度</label>
                    <input type="range" id="directionalIntensity" min="0" max="2" step="0.1" value="0.8">
                    <div class="value-display" id="directionalIntensity-value">0.8</div>
                </div>
                <div class="control-group">
                    <label for="directionalColor">颜色</label>
                    <input type="color" id="directionalColor" value="#ffffff">
                </div>
            </div>
            
            <div class="light-section">
                <h4>💡 点光源 (PointLight)</h4>
                <div class="light-toggle">
                    <input type="checkbox" id="pointToggle" checked>
                    <label for="pointToggle">启用点光源</label>
                </div>
                <div class="control-group">
                    <label for="pointIntensity">强度</label>
                    <input type="range" id="pointIntensity" min="0" max="2" step="0.1" value="0.5">
                    <div class="value-display" id="pointIntensity-value">0.5</div>
                </div>
                <div class="control-group">
                    <label for="pointColor">颜色</label>
                    <input type="color" id="pointColor" value="#ff6b6b">
                </div>
            </div>
            
            <div class="light-section">
                <h4>🔦 聚光灯 (SpotLight)</h4>
                <div class="light-toggle">
                    <input type="checkbox" id="spotToggle">
                    <label for="spotToggle">启用聚光灯</label>
                </div>
                <div class="control-group">
                    <label for="spotIntensity">强度</label>
                    <input type="range" id="spotIntensity" min="0" max="2" step="0.1" value="1">
                    <div class="value-display" id="spotIntensity-value">1.0</div>
                </div>
                <div class="control-group">
                    <label for="spotColor">颜色</label>
                    <input type="color" id="spotColor" value="#4ecdc4">
                </div>
            </div>
            
            <div class="control-group">
                <button id="rotationButton" class="active">🔄 自动旋转</button>
                <button id="helpersButton">👁️ 显示光源辅助器</button>
            </div>
            
            <div class="explanation">
                <h4>💡 光源类型说明</h4>
                <p><strong>环境光</strong>：均匀照亮所有物体，没有方向性</p>
                <p><strong>方向光</strong>：平行光线，模拟太阳光</p>
                <p><strong>点光源</strong>：从一点向四周发光，如灯泡</p>
                <p><strong>聚光灯</strong>：锥形光束，如手电筒</p>
            </div>
        </div>
    </div>
    
    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="lighting-system.js"></script>
</body>
</html>
