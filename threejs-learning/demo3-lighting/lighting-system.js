/**
 * Three.js Demo 3: 光照系统
 * 学习不同类型的光源和光照效果
 *
 * 重点学习：
 * 1. 四种主要光源类型及其特点
 * 2. 光源参数的调节和效果
 * 3. 光源辅助器的使用
 * 4. 材质与光照的交互
 */

// 全局变量
let scene, camera, renderer;
let sphere, plane; // 测试对象
let lights = {}; // 存储所有光源
let helpers = {}; // 存储光源辅助器
let animationId;

// 控制参数
let isRotating = true;
let showHelpers = false;

/**
 * 初始化场景
 */
function init() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x222222); // 深色背景便于观察光照效果

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(0, 3, 8);
  camera.lookAt(0, 0, 0);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.shadowMap.enabled = true; // 启用阴影
  renderer.shadowMap.type = THREE.PCFSoftShadowMap; // 软阴影

  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();
  renderer.setSize(containerRect.width, containerRect.height);
  container.appendChild(renderer.domElement);

  // 创建测试对象
  createObjects();

  // 创建光源
  createLights();

  // 设置控制器
  setupControls();

  // 开始动画循环
  animate();
}

/**
 * 创建测试对象
 * 使用球体和平面来展示光照效果
 */
function createObjects() {
  // 创建球体 - 用于观察光照效果
  const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);
  const sphereMaterial = new THREE.MeshStandardMaterial({
    color: 0x3498db,
    metalness: 0.3,
    roughness: 0.4,
  });
  sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
  sphere.position.y = 1;
  sphere.castShadow = true; // 投射阴影
  scene.add(sphere);

  // 创建地面平面 - 用于接收阴影
  const planeGeometry = new THREE.PlaneGeometry(10, 10);
  const planeMaterial = new THREE.MeshStandardMaterial({
    color: 0x808080,
    metalness: 0.1,
    roughness: 0.8,
  });
  plane = new THREE.Mesh(planeGeometry, planeMaterial);
  plane.rotation.x = -Math.PI / 2; // 旋转90度使其水平
  plane.position.y = -1;
  plane.receiveShadow = true; // 接收阴影
  scene.add(plane);
}

/**
 * 创建各种光源
 */
function createLights() {
  // 1. 环境光 (AmbientLight)
  // 特点：均匀照亮所有物体，没有方向性，不产生阴影
  // 用途：提供基础照明，避免物体完全黑暗
  lights.ambient = new THREE.AmbientLight(0x404040, 0.4);
  scene.add(lights.ambient);

  // 2. 方向光 (DirectionalLight)
  // 特点：平行光线，有方向性，可以产生阴影
  // 用途：模拟太阳光或月光
  lights.directional = new THREE.DirectionalLight(0xffffff, 0.8);
  lights.directional.position.set(5, 5, 5);
  lights.directional.castShadow = true;
  // 设置阴影参数
  lights.directional.shadow.mapSize.width = 1024;
  lights.directional.shadow.mapSize.height = 1024;
  lights.directional.shadow.camera.near = 0.5;
  lights.directional.shadow.camera.far = 50;
  scene.add(lights.directional);

  // 3. 点光源 (PointLight)
  // 特点：从一点向四周发光，有衰减，可以产生阴影
  // 用途：模拟灯泡、火焰等点状光源
  lights.point = new THREE.PointLight(0xff6b6b, 0.5, 100);
  lights.point.position.set(-3, 3, 3);
  lights.point.castShadow = true;
  scene.add(lights.point);

  // 4. 聚光灯 (SpotLight)
  // 特点：锥形光束，有方向和角度，可以产生阴影
  // 用途：模拟手电筒、舞台灯光等
  lights.spot = new THREE.SpotLight(0x4ecdc4, 1, 100, Math.PI / 6, 0.1);
  lights.spot.position.set(3, 5, 3);
  lights.spot.target.position.set(0, 0, 0);
  lights.spot.castShadow = true;
  scene.add(lights.spot);
  scene.add(lights.spot.target); // 聚光灯需要添加目标对象

  // 创建光源辅助器（默认隐藏）
  createLightHelpers();
}

/**
 * 创建光源辅助器
 * 辅助器可以可视化光源的位置和方向
 */
function createLightHelpers() {
  // 方向光辅助器
  helpers.directional = new THREE.DirectionalLightHelper(lights.directional, 1);
  helpers.directional.visible = false;
  scene.add(helpers.directional);

  // 点光源辅助器
  helpers.point = new THREE.PointLightHelper(lights.point, 0.5);
  helpers.point.visible = false;
  scene.add(helpers.point);

  // 聚光灯辅助器
  helpers.spot = new THREE.SpotLightHelper(lights.spot);
  helpers.spot.visible = false;
  scene.add(helpers.spot);
}

/**
 * 动画循环
 */
function animate() {
  animationId = requestAnimationFrame(animate);

  // 旋转球体
  if (isRotating) {
    sphere.rotation.x += 0.01;
    sphere.rotation.y += 0.01;
  }

  // 让点光源绕圆周运动，创建动态光照效果
  const time = Date.now() * 0.001;
  lights.point.position.x = Math.cos(time) * 4;
  lights.point.position.z = Math.sin(time) * 4;

  // 更新辅助器
  if (helpers.spot && helpers.spot.visible) {
    helpers.spot.update();
  }

  // 渲染场景
  renderer.render(scene, camera);
}

/**
 * 设置控制器
 */
function setupControls() {
  // 环境光控制
  setupLightControls("ambient", lights.ambient);

  // 方向光控制
  setupLightControls("directional", lights.directional);

  // 点光源控制
  setupLightControls("point", lights.point);

  // 聚光灯控制
  setupLightControls("spot", lights.spot);

  // 旋转控制
  const rotationButton = document.getElementById("rotationButton");
  rotationButton.addEventListener("click", function () {
    isRotating = !isRotating;
    rotationButton.classList.toggle("active", isRotating);
    rotationButton.textContent = isRotating ? "⏸️ 停止旋转" : "🔄 自动旋转";
  });

  // 辅助器显示控制
  const helpersButton = document.getElementById("helpersButton");
  helpersButton.addEventListener("click", function () {
    showHelpers = !showHelpers;

    // 切换所有辅助器的可见性
    Object.values(helpers).forEach((helper) => {
      helper.visible = showHelpers;
    });

    helpersButton.classList.toggle("active", showHelpers);
    helpersButton.textContent = showHelpers
      ? "👁️ 隐藏辅助器"
      : "👁️ 显示光源辅助器";
  });
}

/**
 * 设置单个光源的控制器
 * @param {string} lightName - 光源名称
 * @param {THREE.Light} light - 光源对象
 */
function setupLightControls(lightName, light) {
  // 开关控制
  const toggle = document.getElementById(`${lightName}Toggle`);
  toggle.addEventListener("change", function () {
    light.visible = this.checked;
  });

  // 强度控制
  const intensitySlider = document.getElementById(`${lightName}Intensity`);
  const intensityValue = document.getElementById(`${lightName}Intensity-value`);

  intensitySlider.addEventListener("input", function () {
    light.intensity = parseFloat(this.value);
    intensityValue.textContent = this.value;
  });

  // 颜色控制
  const colorPicker = document.getElementById(`${lightName}Color`);
  colorPicker.addEventListener("input", function () {
    light.color.setHex(parseInt(this.value.replace("#", "0x")));
  });
}

/**
 * 处理窗口大小变化
 */
function onWindowResize() {
  const container = document.getElementById("canvas-container");
  const containerRect = container.getBoundingClientRect();

  camera.aspect = containerRect.width / containerRect.height;
  camera.updateProjectionMatrix();

  renderer.setSize(containerRect.width, containerRect.height);
}

// 事件监听
window.addEventListener("resize", onWindowResize);
window.addEventListener("load", init);
