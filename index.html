<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 3D图形编程学习中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .learning-paths {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .path-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .path-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }
        
        .path-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .path-card.threejs::before {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
        }
        
        .path-card.webgl::before {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .path-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .path-icon {
            font-size: 3em;
            margin-right: 20px;
        }
        
        .path-title {
            flex: 1;
        }
        
        .path-title h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 5px;
        }
        
        .path-subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .difficulty {
            display: inline-block;
            padding: 6px 15px;
            border-radius: 25px;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .difficulty.recommended {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .difficulty.advanced {
            background: #ffeaa7;
            color: #e17055;
        }
        
        .path-description {
            color: #7f8c8d;
            line-height: 1.7;
            margin-bottom: 20px;
            font-size: 1.05em;
        }
        
        .path-features {
            margin-bottom: 25px;
        }
        
        .path-features ul {
            list-style: none;
            padding: 0;
        }
        
        .path-features li {
            color: #7f8c8d;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .path-features li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .path-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            transition: opacity 0.3s ease;
            width: 100%;
            text-align: center;
        }
        
        .path-card.threejs .path-link {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
        }
        
        .path-card.webgl .path-link {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .path-link:hover {
            opacity: 0.9;
        }
        
        .intro {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .intro h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.8em;
        }
        
        .intro p {
            color: #7f8c8d;
            line-height: 1.8;
            font-size: 1.1em;
        }
        
        .comparison {
            background: #ecf0f1;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .comparison h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
        }
        
        .comparison-table {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            text-align: center;
        }
        
        .comparison-header {
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }
        
        .comparison-cell {
            color: #7f8c8d;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 3D图形编程学习中心</h1>
        
        <div class="intro">
            <h2>欢迎来到3D图形编程的世界！</h2>
            <p>这里提供两条不同的学习路径，帮助你从零开始掌握Web 3D开发技术。选择适合你的学习路径，开始你的3D编程之旅！</p>
        </div>
        
        <div class="learning-paths">
            <div class="path-card threejs" onclick="window.location.href='threejs-learning/index.html'">
                <div class="path-header">
                    <div class="path-icon">🚀</div>
                    <div class="path-title">
                        <h2>Three.js 学习之旅</h2>
                        <div class="path-subtitle">高级3D库，快速上手</div>
                    </div>
                </div>
                
                <div class="difficulty recommended">推荐新手</div>
                
                <div class="path-description">
                    专为新手设计的Three.js学习路径。代码简洁易懂，避免过度封装，通过6个渐进式demo带你快速掌握3D编程核心概念。
                </div>
                
                <div class="path-features">
                    <ul>
                        <li>6个渐进式交互demo</li>
                        <li>详细的中文注释和解释</li>
                        <li>从基础场景到交互控制</li>
                        <li>适合实际项目开发</li>
                        <li>学习周期：1-2周</li>
                    </ul>
                </div>
                
                <a href="threejs-learning/index.html" class="path-link">开始 Three.js 学习</a>
            </div>
            
            <div class="path-card webgl" onclick="window.location.href='webgl-learning/index.html'">
                <div class="path-header">
                    <div class="path-icon">⚡</div>
                    <div class="path-title">
                        <h2>原生 WebGL 深度学习</h2>
                        <div class="path-subtitle">底层图形API，深入理解</div>
                    </div>
                </div>
                
                <div class="difficulty advanced">进阶学习</div>
                
                <div class="path-description">
                    深入学习原生WebGL API，理解3D渲染的底层原理。通过7个专业demo，掌握着色器编程、矩阵变换、光照计算等核心技术。
                </div>
                
                <div class="path-features">
                    <ul>
                        <li>7个专业级WebGL demo</li>
                        <li>着色器编程深度讲解</li>
                        <li>3D数学和渲染原理</li>
                        <li>高级效果和优化技术</li>
                        <li>学习周期：3-4周</li>
                    </ul>
                </div>
                
                <a href="webgl-learning/index.html" class="path-link">开始 WebGL 学习</a>
            </div>
        </div>
        
        <div class="comparison">
            <h3>📊 学习路径对比</h3>
            <div class="comparison-table">
                <div class="comparison-header">特性</div>
                <div class="comparison-header">Three.js</div>
                <div class="comparison-header">原生 WebGL</div>
                
                <div class="comparison-cell">学习难度</div>
                <div class="comparison-cell">⭐⭐⭐</div>
                <div class="comparison-cell">⭐⭐⭐⭐⭐</div>
                
                <div class="comparison-cell">上手速度</div>
                <div class="comparison-cell">快速</div>
                <div class="comparison-cell">较慢</div>
                
                <div class="comparison-cell">适用场景</div>
                <div class="comparison-cell">项目开发</div>
                <div class="comparison-cell">深度定制</div>
                
                <div class="comparison-cell">性能控制</div>
                <div class="comparison-cell">自动优化</div>
                <div class="comparison-cell">完全控制</div>
                
                <div class="comparison-cell">推荐人群</div>
                <div class="comparison-cell">初学者、项目开发者</div>
                <div class="comparison-cell">进阶开发者、研究者</div>
            </div>
        </div>
    </div>
</body>
</html>
