# 🎨 3D图形编程学习中心

一个完整的Web 3D图形编程学习平台，提供两条不同的学习路径，帮助你从零开始掌握3D开发技术。

## 📁 项目结构

```
3d-graphics-learning/
├── index.html                 # 主页 - 学习路径选择
├── threejs-learning/          # Three.js 学习路径
│   ├── index.html            # Three.js 学习主页
│   ├── demo1-basic/          # 基础场景
│   ├── demo2-geometry/       # 几何体和材质
│   ├── demo3-lighting/       # 光照系统
│   ├── demo4-textures/       # 纹理和贴图
│   ├── demo5-animation/      # 动画系统
│   ├── demo6-interaction/    # 交互控制
│   └── README.md             # Three.js 详细说明
└── webgl-learning/           # 原生 WebGL 学习路径
    ├── index.html            # WebGL 学习主页
    ├── demos/                # 7个 WebGL demo
    ├── utils/                # WebGL 工具库
    ├── assets/               # 资源文件
    └── README.md             # WebGL 详细说明
```

## 🚀 两条学习路径

### 🎯 Three.js 学习之旅（推荐新手）

**特点**：
- 🚀 快速上手，适合实际项目开发
- 📚 6个基础demo + 3个进阶demo
- 💡 代码简洁易懂，避免过度封装
- 🎮 丰富的交互控制和参数调节
- ⏱️ 学习周期：2-3周

**学习内容**：
1. **基础场景** - Scene、Camera、Renderer三大核心
2. **几何体和材质** - 各种形状和材质类型
3. **光照系统** - 环境光、方向光、点光源、聚光灯
4. **纹理和贴图** - UV映射、程序化纹理
5. **动画系统** - 动画循环、缓动函数
6. **交互控制** - 鼠标键盘交互、物体拾取
7. **模型加载** - GLTF模型导入和骨骼动画
8. **后处理效果** - 屏幕空间效果和着色器
9. **物理引擎** - Cannon.js集成和碰撞检测
7. **模型加载与骨骼动画** - GLTF模型导入、动画混合器
8. **后处理效果管线** - 屏幕空间效果、自定义着色器

### ⚡ 原生 WebGL 深度学习（进阶学习）

**特点**：
- 🔬 深入理解3D渲染底层原理
- 🎯 7个专业级WebGL demo
- 🧮 着色器编程和3D数学详解
- ⚙️ 完全的性能控制和优化
- ⏱️ 学习周期：3-4周

**学习内容**：
1. **基础三角形** - 着色器、缓冲区、渲染管线
2. **矩形与变换** - 索引缓冲区、2D变换
3. **3D立方体** - 透视投影、深度测试
4. **纹理映射** - UV坐标、纹理过滤
5. **光照系统** - Phong光照模型
6. **交互式场景** - 用户交互、场景管理
7. **高级效果** - 阴影映射、后处理、粒子系统

## 🎯 如何选择学习路径

| 特性 | Three.js | 原生 WebGL |
|------|----------|------------|
| **学习难度** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **上手速度** | 快速 | 较慢 |
| **适用场景** | 项目开发 | 深度定制 |
| **性能控制** | 自动优化 | 完全控制 |
| **推荐人群** | 初学者、项目开发者 | 进阶开发者、研究者 |

### 💡 学习建议

**如果你是完全新手**：
- 建议从 **Three.js** 开始
- 快速建立3D编程概念
- 能够快速看到成果，保持学习兴趣

**如果你想深入理解**：
- 学完Three.js后再学习 **原生WebGL**
- 理解底层原理，提升技术深度
- 为高级优化和自定义效果打基础

**如果你有编程基础**：
- 可以直接学习 **原生WebGL**
- 更深入地理解3D图形学原理
- 为复杂项目开发做准备

## 🚀 快速开始

1. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 或使用任何静态文件服务器
   ```

2. **开始学习**
   - 打开浏览器访问 `http://localhost:8000`
   - 选择适合你的学习路径
   - 按顺序完成每个demo

## 🎓 学习成果

完成学习后，你将掌握：

### Three.js 路径
- ✅ Three.js核心概念和API使用
- ✅ 3D场景的快速搭建
- ✅ 常见3D效果的实现
- ✅ 用户交互的处理
- ✅ 实际项目开发能力

### WebGL 路径
- ✅ WebGL底层API的使用
- ✅ 着色器编程技能
- ✅ 3D数学和图形学原理
- ✅ 性能优化技术
- ✅ 自定义渲染管线能力

## 🛠️ 技术特点

- **无构建依赖**：直接使用CDN，无需复杂配置
- **现代浏览器支持**：兼容所有支持WebGL的浏览器
- **响应式设计**：适配不同屏幕尺寸
- **详细注释**：每行重要代码都有中文解释
- **交互式学习**：丰富的参数控制和实时效果

## 📚 扩展学习资源

- [Three.js官方文档](https://threejs.org/docs/)
- [WebGL基础教程](https://webglfundamentals.org/)
- [3D数学基础](https://www.3dgep.com/)
- [图形学入门](https://learnopengl.com/)

## 📝 许可证

本项目采用 MIT 许可证，可自由使用和修改。

---

**开始你的3D图形编程之旅吧！** 🚀

选择适合你的学习路径，在实践中掌握3D编程的精髓！
