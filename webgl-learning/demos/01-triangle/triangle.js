/**
 * Demo 1: 基础三角形渲染
 * 学习WebGL的基础概念：着色器、缓冲区、渲染管线
 */

// 顶点着色器源码
const vertexShaderSource = `
    // 顶点位置属性
    attribute vec2 a_position;
    // 顶点颜色属性
    attribute vec3 a_color;
    
    // 传递给片段着色器的颜色
    varying vec3 v_color;
    
    // 变换矩阵
    uniform mat3 u_transform;
    
    void main() {
        // 应用变换矩阵到顶点位置
        vec3 position = u_transform * vec3(a_position, 1.0);
        
        // 设置顶点位置（WebGL需要4D坐标）
        gl_Position = vec4(position.xy, 0.0, 1.0);
        
        // 将颜色传递给片段着色器
        v_color = a_color;
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    // 设置浮点数精度
    precision mediump float;
    
    // 从顶点着色器接收的颜色（会被插值）
    varying vec3 v_color;
    
    void main() {
        // 设置像素颜色
        gl_FragColor = vec4(v_color, 1.0);
    }
`;

class TriangleDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.program = null;
        this.buffers = {};
        this.locations = {};
        
        // 控制参数
        this.rotation = 0;
        this.scale = 1;
        this.colors = [
            [1, 0, 0], // 红色
            [0, 1, 0], // 绿色
            [0, 0, 1]  // 蓝色
        ];
        
        this.init();
        this.setupControls();
        this.render();
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.program = WebGLUtils.createProgram(
            this.gl, 
            vertexShaderSource, 
            fragmentShaderSource
        );
        
        // 获取属性和uniform位置
        this.locations = {
            position: this.gl.getAttribLocation(this.program, 'a_position'),
            color: this.gl.getAttribLocation(this.program, 'a_color'),
            transform: this.gl.getUniformLocation(this.program, 'u_transform')
        };
        
        // 创建三角形顶点数据
        this.createTriangle();
        
        // 设置WebGL状态
        this.gl.clearColor(0.9, 0.9, 0.9, 1.0); // 浅灰色背景
        this.gl.enable(this.gl.DEPTH_TEST);
    }
    
    createTriangle() {
        // 三角形顶点位置（标准化设备坐标，范围-1到1）
        const positions = new Float32Array([
            0.0,  0.5,   // 顶部顶点
           -0.5, -0.5,   // 左下顶点
            0.5, -0.5    // 右下顶点
        ]);
        
        // 三角形顶点颜色
        const colors = new Float32Array([
            1.0, 0.0, 0.0,  // 红色
            0.0, 1.0, 0.0,  // 绿色
            0.0, 0.0, 1.0   // 蓝色
        ]);
        
        // 创建位置缓冲区
        this.buffers.position = WebGLUtils.createBuffer(
            this.gl, 
            this.gl.ARRAY_BUFFER, 
            positions
        );
        
        // 创建颜色缓冲区
        this.buffers.color = WebGLUtils.createBuffer(
            this.gl, 
            this.gl.ARRAY_BUFFER, 
            colors
        );
    }
    
    updateColors() {
        // 更新颜色数据
        const colors = new Float32Array([
            ...this.colors[0],
            ...this.colors[1],
            ...this.colors[2]
        ]);
        
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.color);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, colors, this.gl.STATIC_DRAW);
    }
    
    createTransformMatrix() {
        // 创建2D变换矩阵（3x3矩阵用于2D变换）
        const cos = Math.cos(MathUtils.degToRad(this.rotation));
        const sin = Math.sin(MathUtils.degToRad(this.rotation));
        
        // 组合缩放和旋转矩阵
        return new Float32Array([
            cos * this.scale, sin * this.scale, 0,
            -sin * this.scale, cos * this.scale, 0,
            0, 0, 1
        ]);
    }
    
    render() {
        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        
        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);
        
        // 使用着色器程序
        this.gl.useProgram(this.program);
        
        // 设置位置属性
        WebGLUtils.setAttribute(
            this.gl, 
            this.program, 
            'a_position', 
            this.buffers.position, 
            2  // 每个顶点2个分量（x, y）
        );
        
        // 设置颜色属性
        WebGLUtils.setAttribute(
            this.gl, 
            this.program, 
            'a_color', 
            this.buffers.color, 
            3  // 每个顶点3个分量（r, g, b）
        );
        
        // 设置变换矩阵
        const transformMatrix = this.createTransformMatrix();
        this.gl.uniformMatrix3fv(this.locations.transform, false, transformMatrix);
        
        // 绘制三角形
        this.gl.drawArrays(this.gl.TRIANGLES, 0, 3);
        
        // 继续渲染循环
        requestAnimationFrame(() => this.render());
    }
    
    setupControls() {
        // 旋转控制
        const rotationSlider = document.getElementById('rotation');
        const rotationValue = document.getElementById('rotation-value');
        
        rotationSlider.addEventListener('input', (e) => {
            this.rotation = parseFloat(e.target.value);
            rotationValue.textContent = `${this.rotation}°`;
        });
        
        // 缩放控制
        const scaleSlider = document.getElementById('scale');
        const scaleValue = document.getElementById('scale-value');
        
        scaleSlider.addEventListener('input', (e) => {
            this.scale = parseFloat(e.target.value);
            scaleValue.textContent = `${this.scale}x`;
        });
        
        // 颜色控制
        const colorInputs = [
            document.getElementById('color1'),
            document.getElementById('color2'),
            document.getElementById('color3')
        ];
        
        colorInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                const color = this.hexToRgb(e.target.value);
                this.colors[index] = [color.r / 255, color.g / 255, color.b / 255];
                this.updateColors();
            });
        });
    }
    
    // 辅助函数：将十六进制颜色转换为RGB
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new TriangleDemo();
});
