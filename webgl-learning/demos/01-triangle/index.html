<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 1: 基础三角形渲染</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
        }
        
        .controls {
            width: 300px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🔺 基础三角形渲染</h1>
        <p>学习WebGL的基础概念：着色器、缓冲区、渲染管线</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>🎮 交互控制</h3>
            
            <div class="control-group">
                <label for="rotation">旋转角度</label>
                <input type="range" id="rotation" min="0" max="360" value="0">
                <div class="value-display" id="rotation-value">0°</div>
            </div>
            
            <div class="control-group">
                <label for="scale">缩放大小</label>
                <input type="range" id="scale" min="0.1" max="2" step="0.1" value="1">
                <div class="value-display" id="scale-value">1.0x</div>
            </div>
            
            <div class="control-group">
                <label for="color1">顶点1颜色</label>
                <input type="color" id="color1" value="#ff0000">
            </div>
            
            <div class="control-group">
                <label for="color2">顶点2颜色</label>
                <input type="color" id="color2" value="#00ff00">
            </div>
            
            <div class="control-group">
                <label for="color3">顶点3颜色</label>
                <input type="color" id="color3" value="#0000ff">
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>这个demo展示了WebGL的基础概念：</p>
                <p>• <strong>顶点着色器</strong>：处理每个顶点的位置和属性</p>
                <p>• <strong>片段着色器</strong>：决定每个像素的颜色</p>
                <p>• <strong>缓冲区</strong>：存储顶点数据</p>
                <p>• <strong>渲染管线</strong>：从顶点到像素的处理流程</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="triangle.js"></script>
</body>
</html>
