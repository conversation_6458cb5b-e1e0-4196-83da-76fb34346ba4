/**
 * Demo 6: 交互式场景
 * 添加鼠标和键盘交互，实现相机控制和物体操作
 */

// 顶点着色器源码
const vertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_normal;
    
    uniform mat4 u_modelViewProjection;
    uniform mat4 u_model;
    uniform mat4 u_normalMatrix;
    
    varying vec3 v_worldPosition;
    varying vec3 v_normal;
    
    void main() {
        gl_Position = u_modelViewProjection * vec4(a_position, 1.0);
        v_worldPosition = (u_model * vec4(a_position, 1.0)).xyz;
        v_normal = normalize((u_normalMatrix * vec4(a_normal, 0.0)).xyz);
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    precision mediump float;
    
    uniform vec3 u_color;
    uniform bool u_selected;
    uniform vec3 u_lightDirection;
    
    varying vec3 v_worldPosition;
    varying vec3 v_normal;
    
    void main() {
        vec3 normal = normalize(v_normal);
        vec3 lightDir = normalize(-u_lightDirection);
        
        // 简单的漫反射光照
        float diff = max(dot(normal, lightDir), 0.0);
        vec3 ambient = vec3(0.3);
        vec3 diffuse = vec3(0.7) * diff;
        
        vec3 color = u_color * (ambient + diffuse);
        
        // 选中物体高亮
        if (u_selected) {
            color = mix(color, vec3(1.0, 1.0, 0.0), 0.3);
        }
        
        gl_FragColor = vec4(color, 1.0);
    }
`;

class InteractiveDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.program = null;
        this.locations = {};
        
        // 相机系统
        this.camera = {
            position: [0, 0, 5],
            rotation: [0, 0],
            speed: 1.0,
            mouseSensitivity: 1.0
        };
        
        // 输入状态
        this.input = {
            keys: {},
            mouse: {
                x: 0,
                y: 0,
                dragging: false,
                lastX: 0,
                lastY: 0
            }
        };
        
        // 场景对象
        this.objects = [];
        this.selectedObject = null;
        this.objectIdCounter = 0;
        
        // 几何体数据
        this.geometries = {};
        
        this.init();
        this.setupControls();
        this.setupInput();
        this.createInitialScene();
        this.render();
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.program = WebGLUtils.createProgram(
            this.gl, 
            vertexShaderSource, 
            fragmentShaderSource
        );
        
        // 获取属性和uniform位置
        this.locations = {
            position: this.gl.getAttribLocation(this.program, 'a_position'),
            normal: this.gl.getAttribLocation(this.program, 'a_normal'),
            modelViewProjection: this.gl.getUniformLocation(this.program, 'u_modelViewProjection'),
            model: this.gl.getUniformLocation(this.program, 'u_model'),
            normalMatrix: this.gl.getUniformLocation(this.program, 'u_normalMatrix'),
            color: this.gl.getUniformLocation(this.program, 'u_color'),
            selected: this.gl.getUniformLocation(this.program, 'u_selected'),
            lightDirection: this.gl.getUniformLocation(this.program, 'u_lightDirection')
        };
        
        // 创建几何体
        this.createGeometries();
        
        // 设置WebGL状态
        this.gl.clearColor(0.2, 0.2, 0.3, 1.0);
        this.gl.enable(this.gl.DEPTH_TEST);
        this.gl.enable(this.gl.CULL_FACE);
    }
    
    createGeometries() {
        // 创建立方体几何体
        this.geometries.cube = this.createCube();
        
        // 创建球体几何体
        this.geometries.sphere = this.createSphere();
    }
    
    createCube() {
        const vertices = new Float32Array([
            // 前面
            -1, -1,  1,   0,  0,  1,
             1, -1,  1,   0,  0,  1,
             1,  1,  1,   0,  0,  1,
            -1,  1,  1,   0,  0,  1,
            
            // 后面
            -1, -1, -1,   0,  0, -1,
            -1,  1, -1,   0,  0, -1,
             1,  1, -1,   0,  0, -1,
             1, -1, -1,   0,  0, -1,
            
            // 上面
            -1,  1, -1,   0,  1,  0,
            -1,  1,  1,   0,  1,  0,
             1,  1,  1,   0,  1,  0,
             1,  1, -1,   0,  1,  0,
            
            // 下面
            -1, -1, -1,   0, -1,  0,
             1, -1, -1,   0, -1,  0,
             1, -1,  1,   0, -1,  0,
            -1, -1,  1,   0, -1,  0,
            
            // 右面
             1, -1, -1,   1,  0,  0,
             1,  1, -1,   1,  0,  0,
             1,  1,  1,   1,  0,  0,
             1, -1,  1,   1,  0,  0,
            
            // 左面
            -1, -1, -1,  -1,  0,  0,
            -1, -1,  1,  -1,  0,  0,
            -1,  1,  1,  -1,  0,  0,
            -1,  1, -1,  -1,  0,  0
        ]);
        
        const indices = new Uint16Array([
            0,  1,  2,    0,  2,  3,    // 前面
            4,  5,  6,    4,  6,  7,    // 后面
            8,  9,  10,   8,  10, 11,   // 上面
            12, 13, 14,   12, 14, 15,   // 下面
            16, 17, 18,   16, 18, 19,   // 右面
            20, 21, 22,   20, 22, 23    // 左面
        ]);
        
        return {
            vertexBuffer: WebGLUtils.createBuffer(this.gl, this.gl.ARRAY_BUFFER, vertices),
            indexBuffer: WebGLUtils.createBuffer(this.gl, this.gl.ELEMENT_ARRAY_BUFFER, indices),
            indexCount: indices.length
        };
    }
    
    createSphere() {
        const radius = 1;
        const latitudeBands = 16;
        const longitudeBands = 16;
        
        const vertices = [];
        const indices = [];
        
        // 生成球体顶点
        for (let lat = 0; lat <= latitudeBands; lat++) {
            const theta = lat * Math.PI / latitudeBands;
            const sinTheta = Math.sin(theta);
            const cosTheta = Math.cos(theta);
            
            for (let lon = 0; lon <= longitudeBands; lon++) {
                const phi = lon * 2 * Math.PI / longitudeBands;
                const sinPhi = Math.sin(phi);
                const cosPhi = Math.cos(phi);
                
                const x = cosPhi * sinTheta;
                const y = cosTheta;
                const z = sinPhi * sinTheta;
                
                vertices.push(radius * x, radius * y, radius * z);
                vertices.push(x, y, z); // 法线
            }
        }
        
        // 生成索引
        for (let lat = 0; lat < latitudeBands; lat++) {
            for (let lon = 0; lon < longitudeBands; lon++) {
                const first = (lat * (longitudeBands + 1)) + lon;
                const second = first + longitudeBands + 1;
                
                indices.push(first, second, first + 1);
                indices.push(second, second + 1, first + 1);
            }
        }
        
        return {
            vertexBuffer: WebGLUtils.createBuffer(this.gl, this.gl.ARRAY_BUFFER, new Float32Array(vertices)),
            indexBuffer: WebGLUtils.createBuffer(this.gl, this.gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(indices)),
            indexCount: indices.length
        };
    }

    createInitialScene() {
        // 添加一些初始物体
        this.addObject('cube', [-2, 0, 0], [1, 0.5, 0.5]);
        this.addObject('sphere', [2, 0, 0], [0.5, 1, 0.5]);
        this.addObject('cube', [0, 2, 0], [0.5, 0.5, 1]);
    }

    addObject(type, position = [0, 0, 0], color = [0.7, 0.7, 0.7]) {
        const object = {
            id: this.objectIdCounter++,
            type: type,
            position: [...position],
            rotation: [0, 0, 0],
            scale: [1, 1, 1],
            color: [...color]
        };

        this.objects.push(object);
        return object;
    }

    deleteObject(object) {
        const index = this.objects.indexOf(object);
        if (index > -1) {
            this.objects.splice(index, 1);
            if (this.selectedObject === object) {
                this.selectedObject = null;
                this.updateSelectedObjectDisplay();
            }
        }
    }

    updateCamera() {
        const speed = this.camera.speed * 0.1;

        // 键盘移动
        if (this.input.keys['w'] || this.input.keys['W']) {
            this.camera.position[2] -= speed;
        }
        if (this.input.keys['s'] || this.input.keys['S']) {
            this.camera.position[2] += speed;
        }
        if (this.input.keys['a'] || this.input.keys['A']) {
            this.camera.position[0] -= speed;
        }
        if (this.input.keys['d'] || this.input.keys['D']) {
            this.camera.position[0] += speed;
        }
        if (this.input.keys[' ']) {
            this.camera.position[1] += speed;
        }
        if (this.input.keys['Shift']) {
            this.camera.position[1] -= speed;
        }
    }

    createViewMatrix() {
        // 创建相机变换矩阵
        const rotX = MathUtils.rotationX(this.camera.rotation[0]);
        const rotY = MathUtils.rotationY(this.camera.rotation[1]);
        const translation = MathUtils.translation(-this.camera.position[0], -this.camera.position[1], -this.camera.position[2]);

        let viewMatrix = MathUtils.multiply(rotX, rotY);
        viewMatrix = MathUtils.multiply(viewMatrix, translation);

        return viewMatrix;
    }

    render() {
        // 更新相机
        this.updateCamera();

        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);

        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

        // 使用着色器程序
        this.gl.useProgram(this.program);

        // 创建投影矩阵
        const aspect = this.canvas.width / this.canvas.height;
        const projectionMatrix = MathUtils.perspective(
            MathUtils.degToRad(60), aspect, 0.1, 100
        );

        // 创建视图矩阵
        const viewMatrix = this.createViewMatrix();

        // 设置光照方向
        this.gl.uniform3f(this.locations.lightDirection, 0.5, 0.8, 0.3);

        // 渲染所有物体
        for (const object of this.objects) {
            this.renderObject(object, viewMatrix, projectionMatrix);
        }

        // 继续渲染循环
        requestAnimationFrame(() => this.render());
    }

    renderObject(object, viewMatrix, projectionMatrix) {
        // 创建模型矩阵
        const translation = MathUtils.translation(...object.position);
        const rotationX = MathUtils.rotationX(object.rotation[0]);
        const rotationY = MathUtils.rotationY(object.rotation[1]);
        const rotationZ = MathUtils.rotationZ(object.rotation[2]);
        const scale = MathUtils.scaling(...object.scale);

        let modelMatrix = MathUtils.multiply(scale, rotationX);
        modelMatrix = MathUtils.multiply(modelMatrix, rotationY);
        modelMatrix = MathUtils.multiply(modelMatrix, rotationZ);
        modelMatrix = MathUtils.multiply(modelMatrix, translation);

        // 创建MVP矩阵
        let mvpMatrix = MathUtils.multiply(modelMatrix, viewMatrix);
        mvpMatrix = MathUtils.multiply(mvpMatrix, projectionMatrix);

        // 法线矩阵
        const normalMatrix = MathUtils.inverse(modelMatrix);

        // 设置uniform变量
        this.gl.uniformMatrix4fv(this.locations.modelViewProjection, false, mvpMatrix);
        this.gl.uniformMatrix4fv(this.locations.model, false, modelMatrix);
        this.gl.uniformMatrix4fv(this.locations.normalMatrix, false, normalMatrix);
        this.gl.uniform3fv(this.locations.color, object.color);
        this.gl.uniform1i(this.locations.selected, object === this.selectedObject);

        // 获取几何体
        const geometry = this.geometries[object.type];

        // 设置顶点属性
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, geometry.vertexBuffer);

        // 位置属性 (3个分量)
        this.gl.enableVertexAttribArray(this.locations.position);
        this.gl.vertexAttribPointer(this.locations.position, 3, this.gl.FLOAT, false, 6 * 4, 0);

        // 法线属性 (3个分量)
        this.gl.enableVertexAttribArray(this.locations.normal);
        this.gl.vertexAttribPointer(this.locations.normal, 3, this.gl.FLOAT, false, 6 * 4, 3 * 4);

        // 绘制
        this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, geometry.indexBuffer);
        this.gl.drawElements(this.gl.TRIANGLES, geometry.indexCount, this.gl.UNSIGNED_SHORT, 0);
    }

    setupInput() {
        // 键盘事件
        this.canvas.addEventListener('keydown', (e) => {
            this.input.keys[e.key] = true;
            e.preventDefault();
        });

        this.canvas.addEventListener('keyup', (e) => {
            this.input.keys[e.key] = false;
            e.preventDefault();
        });

        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => {
            this.input.mouse.dragging = true;
            this.input.mouse.lastX = e.clientX;
            this.input.mouse.lastY = e.clientY;

            // 物体选择
            this.handleObjectSelection(e);

            this.canvas.focus();
            e.preventDefault();
        });

        this.canvas.addEventListener('mousemove', (e) => {
            if (this.input.mouse.dragging) {
                const deltaX = e.clientX - this.input.mouse.lastX;
                const deltaY = e.clientY - this.input.mouse.lastY;

                this.camera.rotation[1] += deltaX * 0.01 * this.camera.mouseSensitivity;
                this.camera.rotation[0] += deltaY * 0.01 * this.camera.mouseSensitivity;

                // 限制垂直旋转角度
                this.camera.rotation[0] = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.camera.rotation[0]));

                this.input.mouse.lastX = e.clientX;
                this.input.mouse.lastY = e.clientY;
            }
        });

        this.canvas.addEventListener('mouseup', () => {
            this.input.mouse.dragging = false;
        });

        // 滚轮缩放
        this.canvas.addEventListener('wheel', (e) => {
            this.camera.position[2] += e.deltaY * 0.01;
            e.preventDefault();
        });

        // 确保canvas可以接收键盘事件
        this.canvas.setAttribute('tabindex', '0');
        this.canvas.focus();
    }

    handleObjectSelection(e) {
        // 简化的物体选择：基于屏幕位置
        const rect = this.canvas.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
        const y = (1 - (e.clientY - rect.top) / rect.height) * 2 - 1;

        // 找到最近的物体（简化实现）
        let closestObject = null;
        let closestDistance = Infinity;

        for (const object of this.objects) {
            // 计算物体到相机的距离
            const dx = object.position[0] - this.camera.position[0];
            const dy = object.position[1] - this.camera.position[1];
            const dz = object.position[2] - this.camera.position[2];
            const distance = Math.sqrt(dx*dx + dy*dy + dz*dz);

            if (distance < closestDistance) {
                closestDistance = distance;
                closestObject = object;
            }
        }

        this.selectedObject = closestObject;
        this.updateSelectedObjectDisplay();
    }

    updateSelectedObjectDisplay() {
        const display = document.getElementById('selectedObject');
        if (this.selectedObject) {
            display.textContent = `${this.selectedObject.type} #${this.selectedObject.id}`;
        } else {
            display.textContent = '无';
        }
    }

    setupControls() {
        // 相机速度控制
        this.setupSlider('cameraSpeed', 'cameraSpeed-value', (value) => {
            this.camera.speed = value;
            return `${value}x`;
        });

        // 鼠标灵敏度控制
        this.setupSlider('mouseSensitivity', 'mouseSensitivity-value', (value) => {
            this.camera.mouseSensitivity = value;
            return `${value}x`;
        });

        // 重置相机按钮
        document.getElementById('resetCameraButton').addEventListener('click', () => {
            this.camera.position = [0, 0, 5];
            this.camera.rotation = [0, 0];
        });

        // 添加物体按钮
        document.getElementById('addCubeButton').addEventListener('click', () => {
            const x = (Math.random() - 0.5) * 10;
            const y = (Math.random() - 0.5) * 10;
            const z = (Math.random() - 0.5) * 10;
            const color = [Math.random(), Math.random(), Math.random()];
            this.addObject('cube', [x, y, z], color);
        });

        document.getElementById('addSphereButton').addEventListener('click', () => {
            const x = (Math.random() - 0.5) * 10;
            const y = (Math.random() - 0.5) * 10;
            const z = (Math.random() - 0.5) * 10;
            const color = [Math.random(), Math.random(), Math.random()];
            this.addObject('sphere', [x, y, z], color);
        });

        // 删除物体按钮
        document.getElementById('deleteObjectButton').addEventListener('click', () => {
            if (this.selectedObject) {
                this.deleteObject(this.selectedObject);
            }
        });
    }

    setupSlider(sliderId, valueId, updateFn) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);

        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const displayValue = updateFn(value);
            valueDisplay.textContent = displayValue;
        });
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new InteractiveDemo();
});
