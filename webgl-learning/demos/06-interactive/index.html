<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 6: 交互式场景</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
            cursor: grab;
        }
        
        canvas:active {
            cursor: grabbing;
        }
        
        .controls {
            width: 350px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .camera-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8fafc;
        }
        
        .camera-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .instructions {
            background: #e6fffa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #38b2ac;
        }
        
        .instructions h4 {
            color: #234e52;
            margin-bottom: 10px;
        }
        
        .instructions p {
            color: #2d3748;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .key {
            background: #4a5568;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🎮 交互式场景</h1>
        <p>鼠标和键盘交互，相机控制和物体操作</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas" tabindex="0"></canvas>
        </div>
        
        <div class="controls">
            <div class="instructions">
                <h4>🎮 操作说明</h4>
                <p><span class="key">鼠标拖拽</span> - 旋转相机</p>
                <p><span class="key">滚轮</span> - 缩放</p>
                <p><span class="key">WASD</span> - 移动相机</p>
                <p><span class="key">空格</span> - 向上移动</p>
                <p><span class="key">Shift</span> - 向下移动</p>
                <p><span class="key">点击物体</span> - 选择物体</p>
            </div>
            
            <h3>📷 相机控制</h3>
            
            <div class="camera-section">
                <h4>🎯 相机设置</h4>
                <div class="control-group">
                    <label for="cameraSpeed">移动速度</label>
                    <input type="range" id="cameraSpeed" min="0.1" max="2" step="0.1" value="1">
                    <div class="value-display" id="cameraSpeed-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <label for="mouseSensitivity">鼠标灵敏度</label>
                    <input type="range" id="mouseSensitivity" min="0.1" max="2" step="0.1" value="1">
                    <div class="value-display" id="mouseSensitivity-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <button id="resetCameraButton">🔄 重置相机</button>
                </div>
            </div>
            
            <div class="control-group">
                <label>选中物体: <span id="selectedObject">无</span></label>
            </div>
            
            <div class="control-group">
                <button id="addCubeButton">➕ 添加立方体</button>
                <button id="addSphereButton">➕ 添加球体</button>
                <button id="deleteObjectButton">🗑️ 删除选中物体</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>• <strong>鼠标事件</strong>：处理点击、拖拽、滚轮事件</p>
                <p>• <strong>键盘输入</strong>：监听按键状态，实现连续移动</p>
                <p>• <strong>相机系统</strong>：第一人称视角控制</p>
                <p>• <strong>射线检测</strong>：鼠标点击物体选择</p>
                <p>• <strong>场景管理</strong>：动态添加和删除物体</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="interactive.js"></script>
</body>
</html>
