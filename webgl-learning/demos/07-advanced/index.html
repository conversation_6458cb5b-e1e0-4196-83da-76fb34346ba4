<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 7: 高级效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
        }
        
        .controls {
            width: 400px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .effect-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .effect-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .particle-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8fafc;
        }
        
        .particle-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .postprocess-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fef5e7;
        }
        
        .postprocess-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>✨ 高级效果</h1>
        <p>阴影映射、后处理效果和粒子系统</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>✨ 高级效果控制</h3>
            
            <div class="particle-section">
                <h4>🎆 粒子系统</h4>
                <div class="control-group">
                    <label for="particleCount">粒子数量</label>
                    <input type="range" id="particleCount" min="100" max="2000" step="100" value="1000">
                    <div class="value-display" id="particleCount-value">1000</div>
                </div>
                
                <div class="control-group">
                    <label for="particleSpeed">粒子速度</label>
                    <input type="range" id="particleSpeed" min="0.1" max="3" step="0.1" value="1">
                    <div class="value-display" id="particleSpeed-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <label for="particleSize">粒子大小</label>
                    <input type="range" id="particleSize" min="1" max="10" step="0.5" value="3">
                    <div class="value-display" id="particleSize-value">3.0</div>
                </div>
                
                <div class="control-group">
                    <label for="particleColor">粒子颜色</label>
                    <input type="color" id="particleColor" value="#ffaa00">
                </div>
            </div>
            
            <div class="postprocess-section">
                <h4>🎨 后处理效果</h4>
                <div class="control-group">
                    <button id="bloomButton">✨ 辉光效果</button>
                    <button id="blurButton">🌫️ 模糊效果</button>
                    <button id="vignetteButton">🎭 暗角效果</button>
                </div>
                
                <div class="control-group">
                    <label for="effectIntensity">效果强度</label>
                    <input type="range" id="effectIntensity" min="0" max="2" step="0.1" value="1">
                    <div class="value-display" id="effectIntensity-value">1.0</div>
                </div>
            </div>
            
            <div class="effect-section">
                <h4>🌟 场景控制</h4>
                <div class="control-group">
                    <label for="cameraDistance">相机距离</label>
                    <input type="range" id="cameraDistance" min="3" max="15" step="0.5" value="8">
                    <div class="value-display" id="cameraDistance-value">8.0</div>
                </div>
                
                <div class="control-group">
                    <label for="rotationSpeed">旋转速度</label>
                    <input type="range" id="rotationSpeed" min="0" max="3" step="0.1" value="1">
                    <div class="value-display" id="rotationSpeed-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <button id="resetButton">🔄 重置所有效果</button>
                </div>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>• <strong>粒子系统</strong>：大量小物体的高效渲染</p>
                <p>• <strong>帧缓冲</strong>：离屏渲染技术</p>
                <p>• <strong>后处理</strong>：屏幕空间效果处理</p>
                <p>• <strong>辉光效果</strong>：高亮区域的光晕模拟</p>
                <p>• <strong>性能优化</strong>：批量渲染和实例化</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="advanced.js"></script>
</body>
</html>
