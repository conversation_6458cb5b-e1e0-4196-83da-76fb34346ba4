/**
 * Demo 7: 高级效果
 * 实现阴影映射、后处理效果和粒子系统
 */

// 粒子顶点着色器
const particleVertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_velocity;
    attribute float a_life;
    attribute float a_size;
    
    uniform mat4 u_modelViewProjection;
    uniform float u_time;
    uniform float u_particleSize;
    
    varying float v_life;
    
    void main() {
        // 计算粒子位置
        vec3 pos = a_position + a_velocity * u_time;
        
        gl_Position = u_modelViewProjection * vec4(pos, 1.0);
        gl_PointSize = a_size * u_particleSize;
        
        v_life = a_life;
    }
`;

// 粒子片段着色器
const particleFragmentShaderSource = `
    precision mediump float;
    
    uniform vec3 u_color;
    
    varying float v_life;
    
    void main() {
        // 创建圆形粒子
        vec2 coord = gl_PointCoord - vec2(0.5);
        float dist = length(coord);
        
        if (dist > 0.5) {
            discard;
        }
        
        // 根据生命值调整透明度
        float alpha = (1.0 - dist * 2.0) * v_life;
        
        gl_FragColor = vec4(u_color, alpha);
    }
`;

// 后处理顶点着色器
const postProcessVertexShaderSource = `
    attribute vec2 a_position;
    attribute vec2 a_texCoord;
    
    varying vec2 v_texCoord;
    
    void main() {
        gl_Position = vec4(a_position, 0.0, 1.0);
        v_texCoord = a_texCoord;
    }
`;

// 后处理片段着色器
const postProcessFragmentShaderSource = `
    precision mediump float;
    
    uniform sampler2D u_texture;
    uniform int u_effectType;
    uniform float u_intensity;
    uniform vec2 u_resolution;
    
    varying vec2 v_texCoord;
    
    void main() {
        vec4 color = texture2D(u_texture, v_texCoord);
        
        if (u_effectType == 1) {
            // 辉光效果
            vec4 bloom = vec4(0.0);
            float kernel[9];
            kernel[0] = 1.0; kernel[1] = 2.0; kernel[2] = 1.0;
            kernel[3] = 2.0; kernel[4] = 4.0; kernel[5] = 2.0;
            kernel[6] = 1.0; kernel[7] = 2.0; kernel[8] = 1.0;
            
            vec2 offset = 1.0 / u_resolution;
            
            for (int i = 0; i < 3; i++) {
                for (int j = 0; j < 3; j++) {
                    vec2 coord = v_texCoord + vec2(float(i-1), float(j-1)) * offset;
                    bloom += texture2D(u_texture, coord) * kernel[i*3+j] / 16.0;
                }
            }
            
            color = mix(color, bloom, u_intensity * 0.5);
            
        } else if (u_effectType == 2) {
            // 模糊效果
            vec4 blur = vec4(0.0);
            vec2 offset = u_intensity / u_resolution;
            
            blur += texture2D(u_texture, v_texCoord + vec2(-offset.x, -offset.y)) * 0.0625;
            blur += texture2D(u_texture, v_texCoord + vec2(0.0, -offset.y)) * 0.125;
            blur += texture2D(u_texture, v_texCoord + vec2(offset.x, -offset.y)) * 0.0625;
            blur += texture2D(u_texture, v_texCoord + vec2(-offset.x, 0.0)) * 0.125;
            blur += texture2D(u_texture, v_texCoord) * 0.25;
            blur += texture2D(u_texture, v_texCoord + vec2(offset.x, 0.0)) * 0.125;
            blur += texture2D(u_texture, v_texCoord + vec2(-offset.x, offset.y)) * 0.0625;
            blur += texture2D(u_texture, v_texCoord + vec2(0.0, offset.y)) * 0.125;
            blur += texture2D(u_texture, v_texCoord + vec2(offset.x, offset.y)) * 0.0625;
            
            color = blur;
            
        } else if (u_effectType == 3) {
            // 暗角效果
            vec2 center = v_texCoord - vec2(0.5);
            float vignette = 1.0 - dot(center, center) * u_intensity;
            color.rgb *= vignette;
        }
        
        gl_FragColor = color;
    }
`;

class AdvancedDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.programs = {};
        this.buffers = {};
        this.framebuffer = null;
        this.texture = null;
        
        // 粒子系统
        this.particles = [];
        this.particleCount = 1000;
        this.particleSpeed = 1.0;
        this.particleSize = 3.0;
        this.particleColor = [1.0, 0.67, 0.0];
        
        // 后处理效果
        this.postProcessing = {
            enabled: false,
            effectType: 0, // 0: 无, 1: 辉光, 2: 模糊, 3: 暗角
            intensity: 1.0
        };
        
        // 场景控制
        this.scene = {
            cameraDistance: 8,
            rotationSpeed: 1.0,
            time: 0
        };
        
        this.init();
        this.setupControls();
        this.initParticles();
        this.render(0);
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.programs.particle = WebGLUtils.createProgram(
            this.gl, 
            particleVertexShaderSource, 
            particleFragmentShaderSource
        );
        
        this.programs.postProcess = WebGLUtils.createProgram(
            this.gl,
            postProcessVertexShaderSource,
            postProcessFragmentShaderSource
        );
        
        // 创建帧缓冲用于后处理
        this.createFramebuffer();
        
        // 创建全屏四边形用于后处理
        this.createScreenQuad();
        
        // 设置WebGL状态
        this.gl.enable(this.gl.BLEND);
        this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
        this.gl.clearColor(0.1, 0.1, 0.2, 1.0);
    }
    
    createFramebuffer() {
        // 创建帧缓冲
        this.framebuffer = this.gl.createFramebuffer();
        this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, this.framebuffer);
        
        // 创建纹理
        this.texture = this.gl.createTexture();
        this.gl.bindTexture(this.gl.TEXTURE_2D, this.texture);
        this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, 512, 512, 0, this.gl.RGBA, this.gl.UNSIGNED_BYTE, null);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);
        
        // 附加纹理到帧缓冲
        this.gl.framebufferTexture2D(this.gl.FRAMEBUFFER, this.gl.COLOR_ATTACHMENT0, this.gl.TEXTURE_2D, this.texture, 0);
        
        // 创建深度缓冲
        const depthBuffer = this.gl.createRenderbuffer();
        this.gl.bindRenderbuffer(this.gl.RENDERBUFFER, depthBuffer);
        this.gl.renderbufferStorage(this.gl.RENDERBUFFER, this.gl.DEPTH_COMPONENT16, 512, 512);
        this.gl.framebufferRenderbuffer(this.gl.FRAMEBUFFER, this.gl.DEPTH_ATTACHMENT, this.gl.RENDERBUFFER, depthBuffer);
        
        // 恢复默认帧缓冲
        this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
    }
    
    createScreenQuad() {
        const vertices = new Float32Array([
            -1, -1,  0, 0,
             1, -1,  1, 0,
             1,  1,  1, 1,
            -1,  1,  0, 1
        ]);
        
        const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);
        
        this.buffers.screenQuad = WebGLUtils.createBuffer(this.gl, this.gl.ARRAY_BUFFER, vertices);
        this.buffers.screenQuadIndex = WebGLUtils.createBuffer(this.gl, this.gl.ELEMENT_ARRAY_BUFFER, indices);
    }

    initParticles() {
        this.particles = [];
        const positions = [];
        const velocities = [];
        const lives = [];
        const sizes = [];

        for (let i = 0; i < this.particleCount; i++) {
            // 随机位置
            positions.push(
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10
            );

            // 随机速度
            velocities.push(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            );

            // 随机生命值
            lives.push(Math.random());

            // 随机大小
            sizes.push(Math.random() * 5 + 1);
        }

        // 创建粒子缓冲区
        this.buffers.particlePosition = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, new Float32Array(positions)
        );

        this.buffers.particleVelocity = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, new Float32Array(velocities)
        );

        this.buffers.particleLife = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, new Float32Array(lives)
        );

        this.buffers.particleSize = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, new Float32Array(sizes)
        );
    }

    render(currentTime) {
        this.scene.time = currentTime * 0.001;

        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);

        if (this.postProcessing.enabled) {
            // 渲染到帧缓冲
            this.renderToFramebuffer();

            // 后处理渲染到屏幕
            this.renderPostProcess();
        } else {
            // 直接渲染到屏幕
            this.renderScene();
        }

        // 继续渲染循环
        requestAnimationFrame((time) => this.render(time));
    }

    renderToFramebuffer() {
        this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, this.framebuffer);
        this.gl.viewport(0, 0, 512, 512);
        this.renderScene();
        this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
    }

    renderScene() {
        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

        // 创建相机矩阵
        const aspect = this.canvas.width / this.canvas.height;
        const projectionMatrix = MathUtils.perspective(
            MathUtils.degToRad(60), aspect, 0.1, 100
        );

        const cameraAngle = this.scene.time * this.scene.rotationSpeed;
        const cameraX = Math.cos(cameraAngle) * this.scene.cameraDistance;
        const cameraZ = Math.sin(cameraAngle) * this.scene.cameraDistance;

        const viewMatrix = MathUtils.lookAt(
            [cameraX, 2, cameraZ],
            [0, 0, 0],
            [0, 1, 0]
        );

        const mvpMatrix = MathUtils.multiply(viewMatrix, projectionMatrix);

        // 渲染粒子
        this.renderParticles(mvpMatrix);
    }

    renderParticles(mvpMatrix) {
        this.gl.useProgram(this.programs.particle);

        // 设置属性
        const positionLocation = this.gl.getAttribLocation(this.programs.particle, 'a_position');
        const velocityLocation = this.gl.getAttribLocation(this.programs.particle, 'a_velocity');
        const lifeLocation = this.gl.getAttribLocation(this.programs.particle, 'a_life');
        const sizeLocation = this.gl.getAttribLocation(this.programs.particle, 'a_size');

        // 位置属性
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.particlePosition);
        this.gl.enableVertexAttribArray(positionLocation);
        this.gl.vertexAttribPointer(positionLocation, 3, this.gl.FLOAT, false, 0, 0);

        // 速度属性
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.particleVelocity);
        this.gl.enableVertexAttribArray(velocityLocation);
        this.gl.vertexAttribPointer(velocityLocation, 3, this.gl.FLOAT, false, 0, 0);

        // 生命值属性
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.particleLife);
        this.gl.enableVertexAttribArray(lifeLocation);
        this.gl.vertexAttribPointer(lifeLocation, 1, this.gl.FLOAT, false, 0, 0);

        // 大小属性
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.particleSize);
        this.gl.enableVertexAttribArray(sizeLocation);
        this.gl.vertexAttribPointer(sizeLocation, 1, this.gl.FLOAT, false, 0, 0);

        // 设置uniform变量
        const mvpLocation = this.gl.getUniformLocation(this.programs.particle, 'u_modelViewProjection');
        const timeLocation = this.gl.getUniformLocation(this.programs.particle, 'u_time');
        const sizeUniformLocation = this.gl.getUniformLocation(this.programs.particle, 'u_particleSize');
        const colorLocation = this.gl.getUniformLocation(this.programs.particle, 'u_color');

        this.gl.uniformMatrix4fv(mvpLocation, false, mvpMatrix);
        this.gl.uniform1f(timeLocation, this.scene.time * this.particleSpeed);
        this.gl.uniform1f(sizeUniformLocation, this.particleSize);
        this.gl.uniform3fv(colorLocation, this.particleColor);

        // 绘制粒子
        this.gl.drawArrays(this.gl.POINTS, 0, this.particleCount);
    }

    renderPostProcess() {
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        this.gl.useProgram(this.programs.postProcess);

        // 设置属性
        const positionLocation = this.gl.getAttribLocation(this.programs.postProcess, 'a_position');
        const texCoordLocation = this.gl.getAttribLocation(this.programs.postProcess, 'a_texCoord');

        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.screenQuad);

        this.gl.enableVertexAttribArray(positionLocation);
        this.gl.vertexAttribPointer(positionLocation, 2, this.gl.FLOAT, false, 4 * 4, 0);

        this.gl.enableVertexAttribArray(texCoordLocation);
        this.gl.vertexAttribPointer(texCoordLocation, 2, this.gl.FLOAT, false, 4 * 4, 2 * 4);

        // 设置uniform变量
        const textureLocation = this.gl.getUniformLocation(this.programs.postProcess, 'u_texture');
        const effectTypeLocation = this.gl.getUniformLocation(this.programs.postProcess, 'u_effectType');
        const intensityLocation = this.gl.getUniformLocation(this.programs.postProcess, 'u_intensity');
        const resolutionLocation = this.gl.getUniformLocation(this.programs.postProcess, 'u_resolution');

        this.gl.activeTexture(this.gl.TEXTURE0);
        this.gl.bindTexture(this.gl.TEXTURE_2D, this.texture);
        this.gl.uniform1i(textureLocation, 0);
        this.gl.uniform1i(effectTypeLocation, this.postProcessing.effectType);
        this.gl.uniform1f(intensityLocation, this.postProcessing.intensity);
        this.gl.uniform2f(resolutionLocation, this.canvas.width, this.canvas.height);

        // 绘制全屏四边形
        this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.screenQuadIndex);
        this.gl.drawElements(this.gl.TRIANGLES, 6, this.gl.UNSIGNED_SHORT, 0);
    }

    setupControls() {
        // 粒子控制
        this.setupSlider('particleCount', 'particleCount-value', (value) => {
            this.particleCount = parseInt(value);
            this.initParticles();
            return value.toString();
        });

        this.setupSlider('particleSpeed', 'particleSpeed-value', (value) => {
            this.particleSpeed = value;
            return `${value}x`;
        });

        this.setupSlider('particleSize', 'particleSize-value', (value) => {
            this.particleSize = value;
            return value.toFixed(1);
        });

        // 粒子颜色
        document.getElementById('particleColor').addEventListener('input', (e) => {
            const color = this.hexToRgb(e.target.value);
            this.particleColor = [color.r / 255, color.g / 255, color.b / 255];
        });

        // 后处理效果按钮
        document.getElementById('bloomButton').addEventListener('click', () => {
            this.toggleEffect(1, 'bloomButton');
        });

        document.getElementById('blurButton').addEventListener('click', () => {
            this.toggleEffect(2, 'blurButton');
        });

        document.getElementById('vignetteButton').addEventListener('click', () => {
            this.toggleEffect(3, 'vignetteButton');
        });

        // 效果强度
        this.setupSlider('effectIntensity', 'effectIntensity-value', (value) => {
            this.postProcessing.intensity = value;
            return value.toFixed(1);
        });

        // 场景控制
        this.setupSlider('cameraDistance', 'cameraDistance-value', (value) => {
            this.scene.cameraDistance = value;
            return value.toFixed(1);
        });

        this.setupSlider('rotationSpeed', 'rotationSpeed-value', (value) => {
            this.scene.rotationSpeed = value;
            return `${value}x`;
        });

        // 重置按钮
        document.getElementById('resetButton').addEventListener('click', () => {
            this.resetEffects();
        });
    }

    toggleEffect(effectType, buttonId) {
        const button = document.getElementById(buttonId);

        if (this.postProcessing.effectType === effectType) {
            // 关闭效果
            this.postProcessing.enabled = false;
            this.postProcessing.effectType = 0;
            button.classList.remove('active');
        } else {
            // 开启效果
            this.postProcessing.enabled = true;
            this.postProcessing.effectType = effectType;

            // 移除其他按钮的激活状态
            document.querySelectorAll('.postprocess-section button').forEach(btn => {
                btn.classList.remove('active');
            });

            button.classList.add('active');
        }
    }

    resetEffects() {
        // 重置所有参数
        this.particleCount = 1000;
        this.particleSpeed = 1.0;
        this.particleSize = 3.0;
        this.particleColor = [1.0, 0.67, 0.0];

        this.postProcessing.enabled = false;
        this.postProcessing.effectType = 0;
        this.postProcessing.intensity = 1.0;

        this.scene.cameraDistance = 8;
        this.scene.rotationSpeed = 1.0;

        // 重置UI
        document.getElementById('particleCount').value = 1000;
        document.getElementById('particleSpeed').value = 1;
        document.getElementById('particleSize').value = 3;
        document.getElementById('particleColor').value = '#ffaa00';
        document.getElementById('effectIntensity').value = 1;
        document.getElementById('cameraDistance').value = 8;
        document.getElementById('rotationSpeed').value = 1;

        // 更新显示值
        document.getElementById('particleCount-value').textContent = '1000';
        document.getElementById('particleSpeed-value').textContent = '1.0x';
        document.getElementById('particleSize-value').textContent = '3.0';
        document.getElementById('effectIntensity-value').textContent = '1.0';
        document.getElementById('cameraDistance-value').textContent = '8.0';
        document.getElementById('rotationSpeed-value').textContent = '1.0x';

        // 移除按钮激活状态
        document.querySelectorAll('.postprocess-section button').forEach(btn => {
            btn.classList.remove('active');
        });

        // 重新初始化粒子
        this.initParticles();
    }

    setupSlider(sliderId, valueId, updateFn) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);

        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const displayValue = updateFn(value);
            valueDisplay.textContent = displayValue;
        });
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new AdvancedDemo();
});
