<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 3: 3D立方体</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
        }
        
        .controls {
            width: 350px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .transform-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .transform-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .camera-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8fafc;
        }
        
        .camera-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🧊 3D立方体</h1>
        <p>进入3D世界！学习透视投影、深度测试和3D变换</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>🎮 3D控制</h3>
            
            <div class="transform-section">
                <h4>🔄 旋转控制</h4>
                <div class="control-group">
                    <label for="rotationX">X轴旋转</label>
                    <input type="range" id="rotationX" min="0" max="360" value="15">
                    <div class="value-display" id="rotationX-value">15°</div>
                </div>
                
                <div class="control-group">
                    <label for="rotationY">Y轴旋转</label>
                    <input type="range" id="rotationY" min="0" max="360" value="25">
                    <div class="value-display" id="rotationY-value">25°</div>
                </div>
                
                <div class="control-group">
                    <label for="rotationZ">Z轴旋转</label>
                    <input type="range" id="rotationZ" min="0" max="360" value="0">
                    <div class="value-display" id="rotationZ-value">0°</div>
                </div>
            </div>
            
            <div class="camera-section">
                <h4>📷 相机设置</h4>
                <div class="control-group">
                    <label for="fov">视野角度 (FOV)</label>
                    <input type="range" id="fov" min="30" max="120" value="60">
                    <div class="value-display" id="fov-value">60°</div>
                </div>
                
                <div class="control-group">
                    <label for="distance">相机距离</label>
                    <input type="range" id="distance" min="2" max="10" step="0.5" value="5">
                    <div class="value-display" id="distance-value">5.0</div>
                </div>
            </div>
            
            <div class="control-group">
                <button id="autoRotateButton">🔄 自动旋转</button>
                <button id="wireframeButton">📐 线框模式</button>
                <button id="resetButton">🔄 重置视角</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>• <strong>3D坐标系</strong>：X(右)、Y(上)、Z(向外)</p>
                <p>• <strong>透视投影</strong>：模拟真实的3D视觉效果</p>
                <p>• <strong>深度测试</strong>：正确处理前后遮挡关系</p>
                <p>• <strong>模型-视图-投影矩阵</strong>：3D渲染的核心</p>
                <p>• <strong>背面剔除</strong>：提高渲染性能</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="cube.js"></script>
</body>
</html>
