/**
 * Demo 3: 3D立方体
 * 学习透视投影、深度测试和3D变换矩阵
 */

// 顶点着色器源码
const vertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_color;
    
    uniform mat4 u_modelViewProjection;
    
    varying vec3 v_color;
    
    void main() {
        gl_Position = u_modelViewProjection * vec4(a_position, 1.0);
        v_color = a_color;
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    precision mediump float;
    
    varying vec3 v_color;
    
    void main() {
        gl_FragColor = vec4(v_color, 1.0);
    }
`;

class CubeDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.program = null;
        this.buffers = {};
        this.locations = {};
        
        // 3D变换参数
        this.rotation = { x: 15, y: 25, z: 0 };
        this.camera = { fov: 60, distance: 5 };
        
        // 控制状态
        this.autoRotate = false;
        this.wireframe = false;
        this.lastTime = 0;
        
        this.init();
        this.setupControls();
        this.render(0);
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.program = WebGLUtils.createProgram(
            this.gl, 
            vertexShaderSource, 
            fragmentShaderSource
        );
        
        // 获取属性和uniform位置
        this.locations = {
            position: this.gl.getAttribLocation(this.program, 'a_position'),
            color: this.gl.getAttribLocation(this.program, 'a_color'),
            modelViewProjection: this.gl.getUniformLocation(this.program, 'u_modelViewProjection')
        };
        
        // 创建立方体几何体
        this.createCube();
        
        // 设置WebGL状态
        this.gl.clearColor(0.9, 0.9, 0.9, 1.0);
        this.gl.enable(this.gl.DEPTH_TEST);
        this.gl.enable(this.gl.CULL_FACE);
        this.gl.cullFace(this.gl.BACK);
    }
    
    createCube() {
        // 立方体的8个顶点
        const vertices = new Float32Array([
            // 前面
            -1, -1,  1,
             1, -1,  1,
             1,  1,  1,
            -1,  1,  1,
            
            // 后面
            -1, -1, -1,
            -1,  1, -1,
             1,  1, -1,
             1, -1, -1,
            
            // 上面
            -1,  1, -1,
            -1,  1,  1,
             1,  1,  1,
             1,  1, -1,
            
            // 下面
            -1, -1, -1,
             1, -1, -1,
             1, -1,  1,
            -1, -1,  1,
            
            // 右面
             1, -1, -1,
             1,  1, -1,
             1,  1,  1,
             1, -1,  1,
            
            // 左面
            -1, -1, -1,
            -1, -1,  1,
            -1,  1,  1,
            -1,  1, -1
        ]);
        
        // 每个面的颜色
        const colors = new Float32Array([
            // 前面 - 红色
            1, 0, 0,  1, 0, 0,  1, 0, 0,  1, 0, 0,
            // 后面 - 绿色
            0, 1, 0,  0, 1, 0,  0, 1, 0,  0, 1, 0,
            // 上面 - 蓝色
            0, 0, 1,  0, 0, 1,  0, 0, 1,  0, 0, 1,
            // 下面 - 黄色
            1, 1, 0,  1, 1, 0,  1, 1, 0,  1, 1, 0,
            // 右面 - 紫色
            1, 0, 1,  1, 0, 1,  1, 0, 1,  1, 0, 1,
            // 左面 - 青色
            0, 1, 1,  0, 1, 1,  0, 1, 1,  0, 1, 1
        ]);
        
        // 索引数组：每个面由两个三角形组成
        const indices = new Uint16Array([
            0,  1,  2,    0,  2,  3,    // 前面
            4,  5,  6,    4,  6,  7,    // 后面
            8,  9,  10,   8,  10, 11,   // 上面
            12, 13, 14,   12, 14, 15,   // 下面
            16, 17, 18,   16, 18, 19,   // 右面
            20, 21, 22,   20, 22, 23    // 左面
        ]);
        
        // 线框模式的索引
        const wireframeIndices = new Uint16Array([
            // 前面
            0, 1,  1, 2,  2, 3,  3, 0,
            // 后面
            4, 5,  5, 6,  6, 7,  7, 4,
            // 连接前后面
            0, 4,  1, 7,  2, 6,  3, 5
        ]);
        
        // 创建缓冲区
        this.buffers.vertex = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, vertices
        );
        
        this.buffers.color = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, colors
        );
        
        this.buffers.index = WebGLUtils.createBuffer(
            this.gl, this.gl.ELEMENT_ARRAY_BUFFER, indices
        );
        
        this.buffers.wireframeIndex = WebGLUtils.createBuffer(
            this.gl, this.gl.ELEMENT_ARRAY_BUFFER, wireframeIndices
        );
        
        this.indexCount = indices.length;
        this.wireframeIndexCount = wireframeIndices.length;
    }
    
    createModelViewProjectionMatrix() {
        // 模型矩阵：旋转立方体
        const rotX = MathUtils.rotationX(MathUtils.degToRad(this.rotation.x));
        const rotY = MathUtils.rotationY(MathUtils.degToRad(this.rotation.y));
        const rotZ = MathUtils.rotationZ(MathUtils.degToRad(this.rotation.z));
        
        let modelMatrix = MathUtils.multiply(rotX, rotY);
        modelMatrix = MathUtils.multiply(modelMatrix, rotZ);
        
        // 视图矩阵：相机位置
        const viewMatrix = MathUtils.translation(0, 0, -this.camera.distance);
        
        // 投影矩阵：透视投影
        const aspect = this.canvas.width / this.canvas.height;
        const projectionMatrix = MathUtils.perspective(
            MathUtils.degToRad(this.camera.fov),
            aspect,
            0.1,
            100
        );
        
        // 组合矩阵：投影 * 视图 * 模型
        let mvpMatrix = MathUtils.multiply(modelMatrix, viewMatrix);
        mvpMatrix = MathUtils.multiply(mvpMatrix, projectionMatrix);
        
        return mvpMatrix;
    }
    
    render(currentTime) {
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // 自动旋转
        if (this.autoRotate) {
            this.rotation.y += deltaTime * 0.05;
            this.rotation.x += deltaTime * 0.03;
            
            // 更新UI显示
            document.getElementById('rotationY').value = this.rotation.y % 360;
            document.getElementById('rotationX').value = this.rotation.x % 360;
            document.getElementById('rotationY-value').textContent = `${Math.round(this.rotation.y % 360)}°`;
            document.getElementById('rotationX-value').textContent = `${Math.round(this.rotation.x % 360)}°`;
        }
        
        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        
        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);
        
        // 使用着色器程序
        this.gl.useProgram(this.program);
        
        // 设置顶点属性
        WebGLUtils.setAttribute(
            this.gl, this.program, 'a_position', this.buffers.vertex, 3
        );
        
        WebGLUtils.setAttribute(
            this.gl, this.program, 'a_color', this.buffers.color, 3
        );
        
        // 设置MVP矩阵
        const mvpMatrix = this.createModelViewProjectionMatrix();
        this.gl.uniformMatrix4fv(this.locations.modelViewProjection, false, mvpMatrix);
        
        // 绘制立方体
        if (this.wireframe) {
            // 线框模式
            this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.wireframeIndex);
            this.gl.drawElements(this.gl.LINES, this.wireframeIndexCount, this.gl.UNSIGNED_SHORT, 0);
        } else {
            // 实体模式
            this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.index);
            this.gl.drawElements(this.gl.TRIANGLES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);
        }
        
        // 继续渲染循环
        requestAnimationFrame((time) => this.render(time));
    }
    
    setupControls() {
        // 旋转控制
        this.setupSlider('rotationX', 'rotationX-value', (value) => {
            this.rotation.x = value;
            return `${value}°`;
        });
        
        this.setupSlider('rotationY', 'rotationY-value', (value) => {
            this.rotation.y = value;
            return `${value}°`;
        });
        
        this.setupSlider('rotationZ', 'rotationZ-value', (value) => {
            this.rotation.z = value;
            return `${value}°`;
        });
        
        // 相机控制
        this.setupSlider('fov', 'fov-value', (value) => {
            this.camera.fov = value;
            return `${value}°`;
        });
        
        this.setupSlider('distance', 'distance-value', (value) => {
            this.camera.distance = value;
            return value.toFixed(1);
        });
        
        // 按钮控制
        const autoRotateButton = document.getElementById('autoRotateButton');
        autoRotateButton.addEventListener('click', () => {
            this.autoRotate = !this.autoRotate;
            autoRotateButton.classList.toggle('active', this.autoRotate);
            autoRotateButton.textContent = this.autoRotate ? '⏸️ 停止旋转' : '🔄 自动旋转';
        });
        
        const wireframeButton = document.getElementById('wireframeButton');
        wireframeButton.addEventListener('click', () => {
            this.wireframe = !this.wireframe;
            wireframeButton.classList.toggle('active', this.wireframe);
            wireframeButton.textContent = this.wireframe ? '🎨 实体模式' : '📐 线框模式';
        });
        
        const resetButton = document.getElementById('resetButton');
        resetButton.addEventListener('click', () => {
            this.resetView();
        });
    }
    
    setupSlider(sliderId, valueId, updateFn) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);
        
        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const displayValue = updateFn(value);
            valueDisplay.textContent = displayValue;
        });
    }
    
    resetView() {
        this.rotation = { x: 15, y: 25, z: 0 };
        this.camera = { fov: 60, distance: 5 };
        this.autoRotate = false;
        this.wireframe = false;
        
        // 重置UI
        document.getElementById('rotationX').value = 15;
        document.getElementById('rotationY').value = 25;
        document.getElementById('rotationZ').value = 0;
        document.getElementById('fov').value = 60;
        document.getElementById('distance').value = 5;
        
        document.getElementById('rotationX-value').textContent = '15°';
        document.getElementById('rotationY-value').textContent = '25°';
        document.getElementById('rotationZ-value').textContent = '0°';
        document.getElementById('fov-value').textContent = '60°';
        document.getElementById('distance-value').textContent = '5.0';
        
        const autoRotateButton = document.getElementById('autoRotateButton');
        const wireframeButton = document.getElementById('wireframeButton');
        
        autoRotateButton.classList.remove('active');
        wireframeButton.classList.remove('active');
        autoRotateButton.textContent = '🔄 自动旋转';
        wireframeButton.textContent = '📐 线框模式';
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new CubeDemo();
});
