/**
 * Demo 2: 矩形和基础变换
 * 学习索引缓冲区和2D变换：平移、旋转、缩放
 */

// 顶点着色器源码
const vertexShaderSource = `
    attribute vec2 a_position;
    
    uniform mat3 u_transform;
    
    void main() {
        // 应用变换矩阵
        vec3 position = u_transform * vec3(a_position, 1.0);
        gl_Position = vec4(position.xy, 0.0, 1.0);
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    precision mediump float;
    
    uniform vec3 u_color;
    
    void main() {
        gl_FragColor = vec4(u_color, 1.0);
    }
`;

class RectangleDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.program = null;
        this.buffers = {};
        this.locations = {};
        
        // 变换参数
        this.transform = {
            translateX: 0,
            translateY: 0,
            rotation: 0,
            scaleX: 1,
            scaleY: 1
        };
        
        this.color = [0.26, 0.6, 0.88]; // 默认蓝色
        
        this.init();
        this.setupControls();
        this.render();
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.program = WebGLUtils.createProgram(
            this.gl, 
            vertexShaderSource, 
            fragmentShaderSource
        );
        
        // 获取属性和uniform位置
        this.locations = {
            position: this.gl.getAttribLocation(this.program, 'a_position'),
            transform: this.gl.getUniformLocation(this.program, 'u_transform'),
            color: this.gl.getUniformLocation(this.program, 'u_color')
        };
        
        // 创建矩形几何体
        this.createRectangle();
        
        // 设置WebGL状态
        this.gl.clearColor(0.9, 0.9, 0.9, 1.0);
        this.gl.enable(this.gl.DEPTH_TEST);
    }
    
    createRectangle() {
        // 矩形的4个顶点（使用索引缓冲区，避免重复顶点）
        const vertices = new Float32Array([
            -0.3,  0.3,  // 左上
             0.3,  0.3,  // 右上
             0.3, -0.3,  // 右下
            -0.3, -0.3   // 左下
        ]);
        
        // 索引数组：定义两个三角形组成矩形
        const indices = new Uint16Array([
            0, 1, 2,  // 第一个三角形：左上、右上、右下
            0, 2, 3   // 第二个三角形：左上、右下、左下
        ]);
        
        // 创建顶点缓冲区
        this.buffers.vertex = WebGLUtils.createBuffer(
            this.gl,
            this.gl.ARRAY_BUFFER,
            vertices
        );
        
        // 创建索引缓冲区
        this.buffers.index = WebGLUtils.createBuffer(
            this.gl,
            this.gl.ELEMENT_ARRAY_BUFFER,
            indices
        );
        
        this.indexCount = indices.length;
    }
    
    createTransformMatrix() {
        // 创建各种变换矩阵
        const translationMatrix = new Float32Array([
            1, 0, 0,
            0, 1, 0,
            this.transform.translateX, this.transform.translateY, 1
        ]);
        
        const rotationAngle = MathUtils.degToRad(this.transform.rotation);
        const cos = Math.cos(rotationAngle);
        const sin = Math.sin(rotationAngle);
        const rotationMatrix = new Float32Array([
            cos, sin, 0,
            -sin, cos, 0,
            0, 0, 1
        ]);
        
        const scaleMatrix = new Float32Array([
            this.transform.scaleX, 0, 0,
            0, this.transform.scaleY, 0,
            0, 0, 1
        ]);
        
        // 组合变换：先缩放，再旋转，最后平移
        // 注意：矩阵乘法的顺序很重要！
        let result = this.multiplyMatrix3(scaleMatrix, rotationMatrix);
        result = this.multiplyMatrix3(result, translationMatrix);
        
        return result;
    }
    
    // 3x3矩阵乘法
    multiplyMatrix3(a, b) {
        const result = new Float32Array(9);
        
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                result[i * 3 + j] = 
                    a[i * 3 + 0] * b[0 * 3 + j] +
                    a[i * 3 + 1] * b[1 * 3 + j] +
                    a[i * 3 + 2] * b[2 * 3 + j];
            }
        }
        
        return result;
    }
    
    render() {
        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        
        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);
        
        // 使用着色器程序
        this.gl.useProgram(this.program);
        
        // 设置顶点属性
        WebGLUtils.setAttribute(
            this.gl,
            this.program,
            'a_position',
            this.buffers.vertex,
            2
        );
        
        // 设置变换矩阵
        const transformMatrix = this.createTransformMatrix();
        this.gl.uniformMatrix3fv(this.locations.transform, false, transformMatrix);
        
        // 设置颜色
        this.gl.uniform3fv(this.locations.color, this.color);
        
        // 绑定索引缓冲区并绘制
        this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.index);
        this.gl.drawElements(this.gl.TRIANGLES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);
        
        // 继续渲染循环
        requestAnimationFrame(() => this.render());
    }
    
    setupControls() {
        // 平移控制
        this.setupSlider('translateX', 'translateX-value', (value) => {
            this.transform.translateX = value;
            return value.toFixed(1);
        });
        
        this.setupSlider('translateY', 'translateY-value', (value) => {
            this.transform.translateY = value;
            return value.toFixed(1);
        });
        
        // 旋转控制
        this.setupSlider('rotation', 'rotation-value', (value) => {
            this.transform.rotation = value;
            return `${value}°`;
        });
        
        // 缩放控制
        this.setupSlider('scaleX', 'scaleX-value', (value) => {
            this.transform.scaleX = value;
            return `${value}x`;
        });
        
        this.setupSlider('scaleY', 'scaleY-value', (value) => {
            this.transform.scaleY = value;
            return `${value}x`;
        });
        
        // 颜色控制
        const colorInput = document.getElementById('color');
        colorInput.addEventListener('input', (e) => {
            const color = this.hexToRgb(e.target.value);
            this.color = [color.r / 255, color.g / 255, color.b / 255];
        });
        
        // 重置按钮
        const resetButton = document.getElementById('resetButton');
        resetButton.addEventListener('click', () => {
            this.resetTransforms();
        });
    }
    
    setupSlider(sliderId, valueId, updateFn) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);
        
        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const displayValue = updateFn(value);
            valueDisplay.textContent = displayValue;
        });
    }
    
    resetTransforms() {
        // 重置所有变换参数
        this.transform = {
            translateX: 0,
            translateY: 0,
            rotation: 0,
            scaleX: 1,
            scaleY: 1
        };
        
        // 重置UI控件
        document.getElementById('translateX').value = 0;
        document.getElementById('translateY').value = 0;
        document.getElementById('rotation').value = 0;
        document.getElementById('scaleX').value = 1;
        document.getElementById('scaleY').value = 1;
        
        // 更新显示值
        document.getElementById('translateX-value').textContent = '0.0';
        document.getElementById('translateY-value').textContent = '0.0';
        document.getElementById('rotation-value').textContent = '0°';
        document.getElementById('scaleX-value').textContent = '1.0x';
        document.getElementById('scaleY-value').textContent = '1.0x';
    }
    
    // 辅助函数：将十六进制颜色转换为RGB
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new RectangleDemo();
});
