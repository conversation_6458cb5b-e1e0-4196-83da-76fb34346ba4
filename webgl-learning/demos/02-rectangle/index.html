<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 2: 矩形和基础变换</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
        }
        
        .controls {
            width: 320px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .transform-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .transform-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🔲 矩形和基础变换</h1>
        <p>学习索引缓冲区和2D变换：平移、旋转、缩放</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>🎮 变换控制</h3>
            
            <div class="transform-section">
                <h4>📍 平移 (Translation)</h4>
                <div class="control-group">
                    <label for="translateX">X轴平移</label>
                    <input type="range" id="translateX" min="-1" max="1" step="0.1" value="0">
                    <div class="value-display" id="translateX-value">0.0</div>
                </div>
                
                <div class="control-group">
                    <label for="translateY">Y轴平移</label>
                    <input type="range" id="translateY" min="-1" max="1" step="0.1" value="0">
                    <div class="value-display" id="translateY-value">0.0</div>
                </div>
            </div>
            
            <div class="transform-section">
                <h4>🔄 旋转 (Rotation)</h4>
                <div class="control-group">
                    <label for="rotation">旋转角度</label>
                    <input type="range" id="rotation" min="0" max="360" value="0">
                    <div class="value-display" id="rotation-value">0°</div>
                </div>
            </div>
            
            <div class="transform-section">
                <h4>📏 缩放 (Scale)</h4>
                <div class="control-group">
                    <label for="scaleX">X轴缩放</label>
                    <input type="range" id="scaleX" min="0.1" max="2" step="0.1" value="1">
                    <div class="value-display" id="scaleX-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <label for="scaleY">Y轴缩放</label>
                    <input type="range" id="scaleY" min="0.1" max="2" step="0.1" value="1">
                    <div class="value-display" id="scaleY-value">1.0x</div>
                </div>
            </div>
            
            <div class="control-group">
                <label for="color">矩形颜色</label>
                <input type="color" id="color" value="#4299e1">
            </div>
            
            <div class="control-group">
                <button id="resetButton">🔄 重置所有变换</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>• <strong>索引缓冲区</strong>：用4个顶点绘制矩形，避免重复顶点</p>
                <p>• <strong>平移</strong>：改变物体在空间中的位置</p>
                <p>• <strong>旋转</strong>：围绕原点旋转物体</p>
                <p>• <strong>缩放</strong>：改变物体的大小</p>
                <p>• <strong>矩阵变换</strong>：组合多种变换效果</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="rectangle.js"></script>
</body>
</html>
