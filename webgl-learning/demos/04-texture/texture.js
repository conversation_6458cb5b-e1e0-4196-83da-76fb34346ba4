/**
 * Demo 4: 纹理映射
 * 学习纹理加载、UV坐标和纹理过滤
 */

// 顶点着色器源码
const vertexShaderSource = `
    attribute vec3 a_position;
    attribute vec2 a_texCoord;
    
    uniform mat4 u_modelViewProjection;
    uniform vec2 u_uvScale;
    uniform vec2 u_uvOffset;
    
    varying vec2 v_texCoord;
    
    void main() {
        gl_Position = u_modelViewProjection * vec4(a_position, 1.0);
        
        // 应用UV变换
        v_texCoord = (a_texCoord * u_uvScale) + u_uvOffset;
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    precision mediump float;
    
    uniform sampler2D u_texture;
    uniform bool u_showUV;
    
    varying vec2 v_texCoord;
    
    void main() {
        if (u_showUV) {
            // 显示UV网格
            vec2 grid = abs(fract(v_texCoord * 10.0) - 0.5);
            float line = step(0.4, max(grid.x, grid.y));
            vec3 color = mix(vec3(0.8, 0.8, 1.0), vec3(0.2, 0.2, 0.8), line);
            gl_FragColor = vec4(color, 1.0);
        } else {
            // 显示纹理
            gl_FragColor = texture2D(u_texture, v_texCoord);
        }
    }
`;

class TextureDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.program = null;
        this.buffers = {};
        this.locations = {};
        this.textures = {};
        
        // 控制参数
        this.rotation = 25;
        this.uvTransform = {
            scaleX: 1,
            scaleY: 1,
            offsetX: 0,
            offsetY: 0
        };
        
        // 状态
        this.autoRotate = false;
        this.showUV = false;
        this.currentTexture = 'checkerboard';
        this.lastTime = 0;
        
        this.init();
        this.setupControls();
        this.render(0);
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.program = WebGLUtils.createProgram(
            this.gl, 
            vertexShaderSource, 
            fragmentShaderSource
        );
        
        // 获取属性和uniform位置
        this.locations = {
            position: this.gl.getAttribLocation(this.program, 'a_position'),
            texCoord: this.gl.getAttribLocation(this.program, 'a_texCoord'),
            modelViewProjection: this.gl.getUniformLocation(this.program, 'u_modelViewProjection'),
            uvScale: this.gl.getUniformLocation(this.program, 'u_uvScale'),
            uvOffset: this.gl.getUniformLocation(this.program, 'u_uvOffset'),
            texture: this.gl.getUniformLocation(this.program, 'u_texture'),
            showUV: this.gl.getUniformLocation(this.program, 'u_showUV')
        };
        
        // 创建几何体
        this.createGeometry();
        
        // 创建纹理
        this.createTextures();
        
        // 设置WebGL状态
        this.gl.clearColor(0.9, 0.9, 0.9, 1.0);
        this.gl.enable(this.gl.DEPTH_TEST);
        this.gl.enable(this.gl.CULL_FACE);
    }
    
    createGeometry() {
        // 创建一个带纹理坐标的立方体
        const vertices = new Float32Array([
            // 前面 (位置 + UV坐标)
            -1, -1,  1,   0, 0,
             1, -1,  1,   1, 0,
             1,  1,  1,   1, 1,
            -1,  1,  1,   0, 1,
            
            // 后面
            -1, -1, -1,   1, 0,
            -1,  1, -1,   1, 1,
             1,  1, -1,   0, 1,
             1, -1, -1,   0, 0,
            
            // 上面
            -1,  1, -1,   0, 1,
            -1,  1,  1,   0, 0,
             1,  1,  1,   1, 0,
             1,  1, -1,   1, 1,
            
            // 下面
            -1, -1, -1,   0, 0,
             1, -1, -1,   1, 0,
             1, -1,  1,   1, 1,
            -1, -1,  1,   0, 1,
            
            // 右面
             1, -1, -1,   1, 0,
             1,  1, -1,   1, 1,
             1,  1,  1,   0, 1,
             1, -1,  1,   0, 0,
            
            // 左面
            -1, -1, -1,   0, 0,
            -1, -1,  1,   1, 0,
            -1,  1,  1,   1, 1,
            -1,  1, -1,   0, 1
        ]);
        
        // 索引数组
        const indices = new Uint16Array([
            0,  1,  2,    0,  2,  3,    // 前面
            4,  5,  6,    4,  6,  7,    // 后面
            8,  9,  10,   8,  10, 11,   // 上面
            12, 13, 14,   12, 14, 15,   // 下面
            16, 17, 18,   16, 18, 19,   // 右面
            20, 21, 22,   20, 22, 23    // 左面
        ]);
        
        // 创建缓冲区
        this.buffers.vertex = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, vertices
        );
        
        this.buffers.index = WebGLUtils.createBuffer(
            this.gl, this.gl.ELEMENT_ARRAY_BUFFER, indices
        );
        
        this.indexCount = indices.length;
    }
    
    createTextures() {
        // 创建各种程序化纹理
        const textureData = {
            checkerboard: TextureGenerator.createCheckerboard(256, 8, [255, 255, 255], [100, 100, 100]),
            gradient: TextureGenerator.createGradient(256, [255, 100, 100], [100, 100, 255], 'horizontal'),
            noise: TextureGenerator.createNoise(256, 0.8, [150, 150, 150]),
            circle: TextureGenerator.createCircle(256, [255, 255, 100], [100, 100, 255]),
            grid: TextureGenerator.createGrid(256, 32, [50, 50, 50], [200, 200, 200], 3)
        };
        
        // 为每种纹理创建WebGL纹理对象
        for (const [name, data] of Object.entries(textureData)) {
            this.textures[name] = TextureGenerator.createWebGLTexture(this.gl, data);
        }
        
        // 更新纹理预览
        this.updateTexturePreview();
    }

    updateTexturePreview() {
        const preview = document.getElementById('texturePreview');
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        let imageData;
        switch (this.currentTexture) {
            case 'checkerboard':
                imageData = TextureGenerator.createCheckerboard(256, 8, [255, 255, 255], [100, 100, 100]);
                break;
            case 'gradient':
                imageData = TextureGenerator.createGradient(256, [255, 100, 100], [100, 100, 255], 'horizontal');
                break;
            case 'noise':
                imageData = TextureGenerator.createNoise(256, 0.8, [150, 150, 150]);
                break;
            case 'circle':
                imageData = TextureGenerator.createCircle(256, [255, 255, 100], [100, 100, 255]);
                break;
            case 'grid':
                imageData = TextureGenerator.createGrid(256, 32, [50, 50, 50], [200, 200, 200], 3);
                break;
        }

        ctx.putImageData(imageData, 0, 0);
        preview.style.backgroundImage = `url(${canvas.toDataURL()})`;
    }

    createModelViewProjectionMatrix() {
        // 模型矩阵：旋转立方体
        const rotY = MathUtils.rotationY(MathUtils.degToRad(this.rotation));
        const rotX = MathUtils.rotationX(MathUtils.degToRad(15));
        const modelMatrix = MathUtils.multiply(rotX, rotY);

        // 视图矩阵：相机位置
        const viewMatrix = MathUtils.translation(0, 0, -5);

        // 投影矩阵：透视投影
        const aspect = this.canvas.width / this.canvas.height;
        const projectionMatrix = MathUtils.perspective(
            MathUtils.degToRad(60),
            aspect,
            0.1,
            100
        );

        // 组合矩阵
        let mvpMatrix = MathUtils.multiply(modelMatrix, viewMatrix);
        mvpMatrix = MathUtils.multiply(mvpMatrix, projectionMatrix);

        return mvpMatrix;
    }

    render(currentTime) {
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        // 自动旋转
        if (this.autoRotate) {
            this.rotation += deltaTime * 0.05;
            document.getElementById('rotation').value = this.rotation % 360;
            document.getElementById('rotation-value').textContent = `${Math.round(this.rotation % 360)}°`;
        }

        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);

        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

        // 使用着色器程序
        this.gl.useProgram(this.program);

        // 设置顶点属性
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffers.vertex);

        // 位置属性 (3个分量)
        this.gl.enableVertexAttribArray(this.locations.position);
        this.gl.vertexAttribPointer(this.locations.position, 3, this.gl.FLOAT, false, 5 * 4, 0);

        // 纹理坐标属性 (2个分量)
        this.gl.enableVertexAttribArray(this.locations.texCoord);
        this.gl.vertexAttribPointer(this.locations.texCoord, 2, this.gl.FLOAT, false, 5 * 4, 3 * 4);

        // 设置MVP矩阵
        const mvpMatrix = this.createModelViewProjectionMatrix();
        this.gl.uniformMatrix4fv(this.locations.modelViewProjection, false, mvpMatrix);

        // 设置UV变换
        this.gl.uniform2f(this.locations.uvScale, this.uvTransform.scaleX, this.uvTransform.scaleY);
        this.gl.uniform2f(this.locations.uvOffset, this.uvTransform.offsetX, this.uvTransform.offsetY);

        // 设置纹理
        this.gl.activeTexture(this.gl.TEXTURE0);
        this.gl.bindTexture(this.gl.TEXTURE_2D, this.textures[this.currentTexture]);
        this.gl.uniform1i(this.locations.texture, 0);

        // 设置UV显示模式
        this.gl.uniform1i(this.locations.showUV, this.showUV);

        // 绘制立方体
        this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.index);
        this.gl.drawElements(this.gl.TRIANGLES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);

        // 继续渲染循环
        requestAnimationFrame((time) => this.render(time));
    }

    setupControls() {
        // 纹理类型选择
        const textureSelect = document.getElementById('textureType');
        textureSelect.addEventListener('change', (e) => {
            this.currentTexture = e.target.value;
            this.updateTexturePreview();
        });

        // UV变换控制
        this.setupSlider('uvScaleX', 'uvScaleX-value', (value) => {
            this.uvTransform.scaleX = value;
            return `${value}x`;
        });

        this.setupSlider('uvScaleY', 'uvScaleY-value', (value) => {
            this.uvTransform.scaleY = value;
            return `${value}x`;
        });

        this.setupSlider('uvOffsetX', 'uvOffsetX-value', (value) => {
            this.uvTransform.offsetX = value;
            return value.toFixed(1);
        });

        this.setupSlider('uvOffsetY', 'uvOffsetY-value', (value) => {
            this.uvTransform.offsetY = value;
            return value.toFixed(1);
        });

        // 旋转控制
        this.setupSlider('rotation', 'rotation-value', (value) => {
            this.rotation = value;
            return `${value}°`;
        });

        // 按钮控制
        const autoRotateButton = document.getElementById('autoRotateButton');
        autoRotateButton.addEventListener('click', () => {
            this.autoRotate = !this.autoRotate;
            autoRotateButton.classList.toggle('active', this.autoRotate);
            autoRotateButton.textContent = this.autoRotate ? '⏸️ 停止旋转' : '🔄 自动旋转';
        });

        const showUVButton = document.getElementById('showUVButton');
        showUVButton.addEventListener('click', () => {
            this.showUV = !this.showUV;
            showUVButton.classList.toggle('active', this.showUV);
            showUVButton.textContent = this.showUV ? '🖼️ 显示纹理' : '📐 显示UV网格';
        });
    }

    setupSlider(sliderId, valueId, updateFn) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);

        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const displayValue = updateFn(value);
            valueDisplay.textContent = displayValue;
        });
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new TextureDemo();
});
