<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 4: 纹理映射</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
        }
        
        .controls {
            width: 380px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            background: white;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .texture-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .texture-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .texture-preview {
            width: 100%;
            height: 80px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            margin-top: 10px;
            background-size: cover;
            background-position: center;
        }
        
        .uv-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8fafc;
        }
        
        .uv-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>🖼️ 纹理映射</h1>
        <p>学习纹理加载、UV坐标和纹理过滤技术</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>🎨 纹理控制</h3>
            
            <div class="texture-section">
                <h4>🖼️ 纹理选择</h4>
                <div class="control-group">
                    <label for="textureType">纹理类型</label>
                    <select id="textureType">
                        <option value="checkerboard">棋盘格</option>
                        <option value="gradient">渐变</option>
                        <option value="noise">噪声</option>
                        <option value="circle">圆形</option>
                        <option value="grid">网格</option>
                    </select>
                    <div class="texture-preview" id="texturePreview"></div>
                </div>
            </div>
            
            <div class="uv-section">
                <h4>📐 UV坐标控制</h4>
                <div class="control-group">
                    <label for="uvScaleX">U轴缩放</label>
                    <input type="range" id="uvScaleX" min="0.1" max="5" step="0.1" value="1">
                    <div class="value-display" id="uvScaleX-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <label for="uvScaleY">V轴缩放</label>
                    <input type="range" id="uvScaleY" min="0.1" max="5" step="0.1" value="1">
                    <div class="value-display" id="uvScaleY-value">1.0x</div>
                </div>
                
                <div class="control-group">
                    <label for="uvOffsetX">U轴偏移</label>
                    <input type="range" id="uvOffsetX" min="-2" max="2" step="0.1" value="0">
                    <div class="value-display" id="uvOffsetX-value">0.0</div>
                </div>
                
                <div class="control-group">
                    <label for="uvOffsetY">V轴偏移</label>
                    <input type="range" id="uvOffsetY" min="-2" max="2" step="0.1" value="0">
                    <div class="value-display" id="uvOffsetY-value">0.0</div>
                </div>
            </div>
            
            <div class="control-group">
                <label for="rotation">旋转角度</label>
                <input type="range" id="rotation" min="0" max="360" value="25">
                <div class="value-display" id="rotation-value">25°</div>
            </div>
            
            <div class="control-group">
                <button id="autoRotateButton">🔄 自动旋转</button>
                <button id="showUVButton">📐 显示UV网格</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>• <strong>UV坐标</strong>：纹理坐标系统，U(0-1)对应X轴，V(0-1)对应Y轴</p>
                <p>• <strong>纹理过滤</strong>：LINEAR vs NEAREST，影响纹理的平滑度</p>
                <p>• <strong>纹理包装</strong>：REPEAT vs CLAMP_TO_EDGE，处理UV超出范围</p>
                <p>• <strong>程序化纹理</strong>：通过代码生成纹理，无需外部图片</p>
                <p>• <strong>纹理坐标变换</strong>：缩放、偏移UV坐标实现不同效果</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="../../utils/texture-generator.js"></script>
    <script src="texture.js"></script>
</body>
</html>
