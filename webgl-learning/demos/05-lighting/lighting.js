/**
 * Demo 5: 光照系统
 * 实现基础光照模型（环境光、漫反射、镜面反射）
 */

// 顶点着色器源码
const vertexShaderSource = `
    attribute vec3 a_position;
    attribute vec3 a_normal;
    
    uniform mat4 u_modelViewProjection;
    uniform mat4 u_model;
    uniform mat4 u_normalMatrix;
    
    varying vec3 v_worldPosition;
    varying vec3 v_normal;
    
    void main() {
        gl_Position = u_modelViewProjection * vec4(a_position, 1.0);
        
        // 世界坐标位置
        v_worldPosition = (u_model * vec4(a_position, 1.0)).xyz;
        
        // 变换法线到世界坐标
        v_normal = normalize((u_normalMatrix * vec4(a_normal, 0.0)).xyz);
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    precision mediump float;
    
    uniform vec3 u_lightDirection;
    uniform vec3 u_lightColor;
    uniform float u_lightIntensity;
    
    uniform vec3 u_ambientColor;
    uniform float u_ambientIntensity;
    
    uniform vec3 u_materialColor;
    uniform float u_shininess;
    
    uniform vec3 u_cameraPosition;
    uniform bool u_showNormals;
    
    varying vec3 v_worldPosition;
    varying vec3 v_normal;
    
    void main() {
        if (u_showNormals) {
            // 显示法线（调试用）
            gl_FragColor = vec4(v_normal * 0.5 + 0.5, 1.0);
            return;
        }
        
        vec3 normal = normalize(v_normal);
        vec3 lightDir = normalize(-u_lightDirection);
        vec3 viewDir = normalize(u_cameraPosition - v_worldPosition);
        
        // 环境光
        vec3 ambient = u_ambientColor * u_ambientIntensity;
        
        // 漫反射
        float diff = max(dot(normal, lightDir), 0.0);
        vec3 diffuse = u_lightColor * u_lightIntensity * diff;
        
        // 镜面反射 (Phong模型)
        vec3 reflectDir = reflect(-lightDir, normal);
        float spec = pow(max(dot(viewDir, reflectDir), 0.0), u_shininess);
        vec3 specular = u_lightColor * u_lightIntensity * spec;
        
        // 组合所有光照
        vec3 result = (ambient + diffuse + specular) * u_materialColor;
        
        gl_FragColor = vec4(result, 1.0);
    }
`;

class LightingDemo {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.gl = null;
        this.program = null;
        this.buffers = {};
        this.locations = {};
        
        // 光照参数
        this.lighting = {
            ambientColor: [1, 1, 1],
            ambientIntensity: 0.3,
            lightDirection: [0.5, 0.8, 0.3],
            lightColor: [1, 1, 1],
            lightIntensity: 1.0,
            materialColor: [0.26, 0.6, 0.88],
            shininess: 32
        };
        
        // 控制参数
        this.rotation = 0;
        this.autoRotate = false;
        this.showNormals = false;
        this.lastTime = 0;
        
        this.init();
        this.setupControls();
        this.render(0);
    }
    
    init() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl');
        if (!this.gl) {
            alert('你的浏览器不支持WebGL');
            return;
        }
        
        // 创建着色器程序
        this.program = WebGLUtils.createProgram(
            this.gl, 
            vertexShaderSource, 
            fragmentShaderSource
        );
        
        // 获取属性和uniform位置
        this.locations = {
            position: this.gl.getAttribLocation(this.program, 'a_position'),
            normal: this.gl.getAttribLocation(this.program, 'a_normal'),
            modelViewProjection: this.gl.getUniformLocation(this.program, 'u_modelViewProjection'),
            model: this.gl.getUniformLocation(this.program, 'u_model'),
            normalMatrix: this.gl.getUniformLocation(this.program, 'u_normalMatrix'),
            lightDirection: this.gl.getUniformLocation(this.program, 'u_lightDirection'),
            lightColor: this.gl.getUniformLocation(this.program, 'u_lightColor'),
            lightIntensity: this.gl.getUniformLocation(this.program, 'u_lightIntensity'),
            ambientColor: this.gl.getUniformLocation(this.program, 'u_ambientColor'),
            ambientIntensity: this.gl.getUniformLocation(this.program, 'u_ambientIntensity'),
            materialColor: this.gl.getUniformLocation(this.program, 'u_materialColor'),
            shininess: this.gl.getUniformLocation(this.program, 'u_shininess'),
            cameraPosition: this.gl.getUniformLocation(this.program, 'u_cameraPosition'),
            showNormals: this.gl.getUniformLocation(this.program, 'u_showNormals')
        };
        
        // 创建几何体
        this.createSphere();
        
        // 设置WebGL状态
        this.gl.clearColor(0.1, 0.1, 0.1, 1.0);
        this.gl.enable(this.gl.DEPTH_TEST);
        this.gl.enable(this.gl.CULL_FACE);
    }
    
    createSphere() {
        const radius = 1;
        const latitudeBands = 30;
        const longitudeBands = 30;
        
        const vertices = [];
        const normals = [];
        const indices = [];
        
        // 生成球体顶点和法线
        for (let lat = 0; lat <= latitudeBands; lat++) {
            const theta = lat * Math.PI / latitudeBands;
            const sinTheta = Math.sin(theta);
            const cosTheta = Math.cos(theta);
            
            for (let lon = 0; lon <= longitudeBands; lon++) {
                const phi = lon * 2 * Math.PI / longitudeBands;
                const sinPhi = Math.sin(phi);
                const cosPhi = Math.cos(phi);
                
                const x = cosPhi * sinTheta;
                const y = cosTheta;
                const z = sinPhi * sinTheta;
                
                vertices.push(radius * x, radius * y, radius * z);
                normals.push(x, y, z);
            }
        }
        
        // 生成索引
        for (let lat = 0; lat < latitudeBands; lat++) {
            for (let lon = 0; lon < longitudeBands; lon++) {
                const first = (lat * (longitudeBands + 1)) + lon;
                const second = first + longitudeBands + 1;
                
                indices.push(first, second, first + 1);
                indices.push(second, second + 1, first + 1);
            }
        }
        
        // 创建缓冲区
        this.buffers.vertex = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, new Float32Array(vertices)
        );
        
        this.buffers.normal = WebGLUtils.createBuffer(
            this.gl, this.gl.ARRAY_BUFFER, new Float32Array(normals)
        );
        
        this.buffers.index = WebGLUtils.createBuffer(
            this.gl, this.gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(indices)
        );
        
        this.indexCount = indices.length;
    }

    render(currentTime) {
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        // 自动旋转
        if (this.autoRotate) {
            this.rotation += deltaTime * 0.05;
        }

        // 调整canvas大小
        WebGLUtils.resizeCanvasToDisplaySize(this.canvas);
        this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);

        // 清除画布
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

        // 使用着色器程序
        this.gl.useProgram(this.program);

        // 设置顶点属性
        WebGLUtils.setAttribute(
            this.gl, this.program, 'a_position', this.buffers.vertex, 3
        );

        WebGLUtils.setAttribute(
            this.gl, this.program, 'a_normal', this.buffers.normal, 3
        );

        // 创建变换矩阵
        const modelMatrix = MathUtils.rotationY(this.rotation);
        const viewMatrix = MathUtils.translation(0, 0, -5);
        const aspect = this.canvas.width / this.canvas.height;
        const projectionMatrix = MathUtils.perspective(
            MathUtils.degToRad(60), aspect, 0.1, 100
        );

        let mvpMatrix = MathUtils.multiply(modelMatrix, viewMatrix);
        mvpMatrix = MathUtils.multiply(mvpMatrix, projectionMatrix);

        // 法线矩阵（模型矩阵的逆转置）
        const normalMatrix = MathUtils.inverse(modelMatrix);

        // 设置uniform变量
        this.gl.uniformMatrix4fv(this.locations.modelViewProjection, false, mvpMatrix);
        this.gl.uniformMatrix4fv(this.locations.model, false, modelMatrix);
        this.gl.uniformMatrix4fv(this.locations.normalMatrix, false, normalMatrix);

        // 光照参数
        this.gl.uniform3fv(this.locations.lightDirection, this.lighting.lightDirection);
        this.gl.uniform3fv(this.locations.lightColor, this.lighting.lightColor);
        this.gl.uniform1f(this.locations.lightIntensity, this.lighting.lightIntensity);

        this.gl.uniform3fv(this.locations.ambientColor, this.lighting.ambientColor);
        this.gl.uniform1f(this.locations.ambientIntensity, this.lighting.ambientIntensity);

        this.gl.uniform3fv(this.locations.materialColor, this.lighting.materialColor);
        this.gl.uniform1f(this.locations.shininess, this.lighting.shininess);

        this.gl.uniform3f(this.locations.cameraPosition, 0, 0, 5);
        this.gl.uniform1i(this.locations.showNormals, this.showNormals);

        // 绘制球体
        this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.buffers.index);
        this.gl.drawElements(this.gl.TRIANGLES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);

        // 继续渲染循环
        requestAnimationFrame((time) => this.render(time));
    }

    setupControls() {
        // 环境光控制
        this.setupSlider('ambientIntensity', 'ambientIntensity-value', (value) => {
            this.lighting.ambientIntensity = value;
            return value.toFixed(1);
        });

        // 方向光控制
        this.setupSlider('lightIntensity', 'lightIntensity-value', (value) => {
            this.lighting.lightIntensity = value;
            return value.toFixed(1);
        });

        this.setupSlider('lightX', 'lightX-value', (value) => {
            this.lighting.lightDirection[0] = value;
            return value.toFixed(1);
        });

        this.setupSlider('lightY', 'lightY-value', (value) => {
            this.lighting.lightDirection[1] = value;
            return value.toFixed(1);
        });

        this.setupSlider('lightZ', 'lightZ-value', (value) => {
            this.lighting.lightDirection[2] = value;
            return value.toFixed(1);
        });

        // 材质控制
        this.setupSlider('shininess', 'shininess-value', (value) => {
            this.lighting.shininess = value;
            return value.toString();
        });

        // 颜色控制
        this.setupColorControl('ambientColor', (color) => {
            this.lighting.ambientColor = color;
        });

        this.setupColorControl('lightColor', (color) => {
            this.lighting.lightColor = color;
        });

        this.setupColorControl('materialColor', (color) => {
            this.lighting.materialColor = color;
        });

        // 按钮控制
        const autoRotateButton = document.getElementById('autoRotateButton');
        autoRotateButton.addEventListener('click', () => {
            this.autoRotate = !this.autoRotate;
            autoRotateButton.classList.toggle('active', this.autoRotate);
            autoRotateButton.textContent = this.autoRotate ? '⏸️ 停止旋转' : '🔄 自动旋转';
        });

        const showNormalsButton = document.getElementById('showNormalsButton');
        showNormalsButton.addEventListener('click', () => {
            this.showNormals = !this.showNormals;
            showNormalsButton.classList.toggle('active', this.showNormals);
            showNormalsButton.textContent = this.showNormals ? '💡 显示光照' : '📐 显示法线';
        });
    }

    setupSlider(sliderId, valueId, updateFn) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);

        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            const displayValue = updateFn(value);
            valueDisplay.textContent = displayValue;
        });
    }

    setupColorControl(colorId, updateFn) {
        const colorInput = document.getElementById(colorId);
        colorInput.addEventListener('input', (e) => {
            const color = this.hexToRgb(e.target.value);
            updateFn([color.r / 255, color.g / 255, color.b / 255]);
        });
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
}

// 页面加载完成后启动demo
window.addEventListener('load', () => {
    new LightingDemo();
});
