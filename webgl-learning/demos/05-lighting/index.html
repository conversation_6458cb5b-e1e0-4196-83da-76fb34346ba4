<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo 5: 光照系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            width: 100%;
        }
        
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        canvas {
            width: 100%;
            height: 500px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
        }
        
        .controls {
            width: 400px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .controls h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            color: #718096;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .control-group input[type="color"] {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        
        .control-group button:hover {
            opacity: 0.9;
        }
        
        .control-group button.active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .value-display {
            color: #4a5568;
            font-size: 0.9em;
            text-align: right;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .explanation {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .explanation h4 {
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .explanation p {
            color: #718096;
            line-height: 1.6;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .lighting-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .lighting-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .light-section {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8fafc;
        }
        
        .light-section h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← 返回主页</a>
    
    <div class="header">
        <h1>💡 光照系统</h1>
        <p>实现真实的光照效果：环境光、漫反射和镜面反射</p>
    </div>
    
    <div class="container">
        <div class="canvas-container">
            <canvas id="webgl-canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>💡 光照控制</h3>
            
            <div class="light-section">
                <h4>🌅 环境光 (Ambient)</h4>
                <div class="control-group">
                    <label for="ambientIntensity">强度</label>
                    <input type="range" id="ambientIntensity" min="0" max="1" step="0.1" value="0.3">
                    <div class="value-display" id="ambientIntensity-value">0.3</div>
                </div>
                
                <div class="control-group">
                    <label for="ambientColor">颜色</label>
                    <input type="color" id="ambientColor" value="#ffffff">
                </div>
            </div>
            
            <div class="light-section">
                <h4>☀️ 方向光 (Directional)</h4>
                <div class="control-group">
                    <label for="lightIntensity">强度</label>
                    <input type="range" id="lightIntensity" min="0" max="2" step="0.1" value="1">
                    <div class="value-display" id="lightIntensity-value">1.0</div>
                </div>
                
                <div class="control-group">
                    <label for="lightColor">颜色</label>
                    <input type="color" id="lightColor" value="#ffffff">
                </div>
                
                <div class="control-group">
                    <label for="lightX">X方向</label>
                    <input type="range" id="lightX" min="-1" max="1" step="0.1" value="0.5">
                    <div class="value-display" id="lightX-value">0.5</div>
                </div>
                
                <div class="control-group">
                    <label for="lightY">Y方向</label>
                    <input type="range" id="lightY" min="-1" max="1" step="0.1" value="0.8">
                    <div class="value-display" id="lightY-value">0.8</div>
                </div>
                
                <div class="control-group">
                    <label for="lightZ">Z方向</label>
                    <input type="range" id="lightZ" min="-1" max="1" step="0.1" value="0.3">
                    <div class="value-display" id="lightZ-value">0.3</div>
                </div>
            </div>
            
            <div class="lighting-section">
                <h4>✨ 材质属性</h4>
                <div class="control-group">
                    <label for="shininess">光泽度</label>
                    <input type="range" id="shininess" min="1" max="128" value="32">
                    <div class="value-display" id="shininess-value">32</div>
                </div>
                
                <div class="control-group">
                    <label for="materialColor">材质颜色</label>
                    <input type="color" id="materialColor" value="#4299e1">
                </div>
            </div>
            
            <div class="control-group">
                <button id="autoRotateButton">🔄 自动旋转</button>
                <button id="showNormalsButton">📐 显示法线</button>
            </div>
            
            <div class="explanation">
                <h4>💡 学习要点</h4>
                <p>• <strong>环境光</strong>：模拟环境中的间接光照</p>
                <p>• <strong>漫反射</strong>：表面粗糙度，光线向各方向散射</p>
                <p>• <strong>镜面反射</strong>：表面光滑度，产生高光效果</p>
                <p>• <strong>法线向量</strong>：决定光照计算的关键因素</p>
                <p>• <strong>Phong光照模型</strong>：经典的光照计算方法</p>
            </div>
        </div>
    </div>
    
    <script src="../../utils/webgl-utils.js"></script>
    <script src="../../utils/math-utils.js"></script>
    <script src="lighting.js"></script>
</body>
</html>
