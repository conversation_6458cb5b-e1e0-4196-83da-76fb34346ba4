<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原生 WebGL 深度学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .demo-card h3 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .demo-card p {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .difficulty {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .difficulty.beginner {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .difficulty.intermediate {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .difficulty.advanced {
            background: #e9d8fd;
            color: #44337a;
        }
        
        .demo-link {
            display: inline-block;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: opacity 0.3s ease;
        }
        
        .demo-link:hover {
            opacity: 0.9;
        }
        
        .intro {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .intro h2 {
            color: #4a5568;
            margin-bottom: 15px;
        }
        
        .intro p {
            color: #718096;
            line-height: 1.8;
            margin-bottom: 15px;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-button">← 返回主页</a>
    
    <div class="container">
        <h1>⚡ 原生 WebGL 深度学习</h1>
        
        <div class="intro">
            <h2>深入理解3D渲染的底层原理</h2>
            <p>这是一个精心设计的WebGL学习路径，从最基础的概念开始，逐步深入到高级的3D图形编程技术。每个demo都包含详细的注释和交互式元素，让你在实践中掌握WebGL的核心概念。</p>
            <p><strong>学习建议：</strong>按照顺序完成每个demo，每个demo都会在前一个的基础上引入新的概念。不要急于求成，理解每个概念后再进入下一个阶段。</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card" onclick="window.location.href='demos/01-triangle/index.html'">
                <div class="difficulty beginner">初级</div>
                <h3>01. 基础三角形</h3>
                <p>学习WebGL的基础概念：着色器、缓冲区、渲染管线。创建你的第一个彩色三角形。</p>
                <a href="demos/01-triangle/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demos/02-rectangle/index.html'">
                <div class="difficulty beginner">初级</div>
                <h3>02. 矩形与变换</h3>
                <p>学习索引缓冲区和基础的2D变换：平移、旋转、缩放。</p>
                <a href="demos/02-rectangle/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demos/03-cube/index.html'">
                <div class="difficulty intermediate">中级</div>
                <h3>03. 3D立方体</h3>
                <p>进入3D世界：透视投影、深度测试、3D变换矩阵。</p>
                <a href="demos/03-cube/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demos/04-texture/index.html'">
                <div class="difficulty intermediate">中级</div>
                <h3>04. 纹理映射</h3>
                <p>学习纹理加载、UV坐标系统和纹理过滤技术。</p>
                <a href="demos/04-texture/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demos/05-lighting/index.html'">
                <div class="difficulty intermediate">中级</div>
                <h3>05. 光照系统</h3>
                <p>实现真实的光照效果：环境光、漫反射、镜面反射。</p>
                <a href="demos/05-lighting/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demos/06-interactive/index.html'">
                <div class="difficulty advanced">高级</div>
                <h3>06. 交互式场景</h3>
                <p>添加用户交互：鼠标控制、键盘输入、物体拾取。</p>
                <a href="demos/06-interactive/index.html" class="demo-link">开始学习</a>
            </div>
            
            <div class="demo-card" onclick="window.location.href='demos/07-advanced/index.html'">
                <div class="difficulty advanced">高级</div>
                <h3>07. 高级效果</h3>
                <p>掌握高级技术：阴影映射、后处理效果、粒子系统。</p>
                <a href="demos/07-advanced/index.html" class="demo-link">开始学习</a>
            </div>
        </div>
    </div>
</body>
</html>
