/**
 * WebGL 工具函数库
 * 提供常用的WebGL操作函数，简化学习过程
 */

class WebGLUtils {
    /**
     * 创建着色器
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {number} type - 着色器类型 (gl.VERTEX_SHADER 或 gl.FRAGMENT_SHADER)
     * @param {string} source - 着色器源码
     * @returns {WebGLShader} 编译好的着色器
     */
    static createShader(gl, type, source) {
        const shader = gl.createShader(type);
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            const info = gl.getShaderInfoLog(shader);
            gl.deleteShader(shader);
            throw new Error(`着色器编译失败: ${info}`);
        }
        
        return shader;
    }
    
    /**
     * 创建着色器程序
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {string} vertexShaderSource - 顶点着色器源码
     * @param {string} fragmentShaderSource - 片段着色器源码
     * @returns {WebGLProgram} 链接好的着色器程序
     */
    static createProgram(gl, vertexShaderSource, fragmentShaderSource) {
        const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
        const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
        
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);
        
        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            const info = gl.getProgramInfoLog(program);
            gl.deleteProgram(program);
            throw new Error(`着色器程序链接失败: ${info}`);
        }
        
        return program;
    }
    
    /**
     * 创建缓冲区
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {number} target - 缓冲区目标 (gl.ARRAY_BUFFER 或 gl.ELEMENT_ARRAY_BUFFER)
     * @param {ArrayBuffer|ArrayBufferView} data - 数据
     * @param {number} usage - 使用方式 (默认 gl.STATIC_DRAW)
     * @returns {WebGLBuffer} 创建的缓冲区
     */
    static createBuffer(gl, target, data, usage = gl.STATIC_DRAW) {
        const buffer = gl.createBuffer();
        gl.bindBuffer(target, buffer);
        gl.bufferData(target, data, usage);
        return buffer;
    }
    
    /**
     * 设置属性
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {WebGLProgram} program - 着色器程序
     * @param {string} name - 属性名
     * @param {WebGLBuffer} buffer - 缓冲区
     * @param {number} size - 每个顶点的分量数
     * @param {number} type - 数据类型 (默认 gl.FLOAT)
     * @param {boolean} normalize - 是否归一化 (默认 false)
     * @param {number} stride - 步长 (默认 0)
     * @param {number} offset - 偏移 (默认 0)
     */
    static setAttribute(gl, program, name, buffer, size, type = gl.FLOAT, normalize = false, stride = 0, offset = 0) {
        const location = gl.getAttribLocation(program, name);
        if (location === -1) {
            console.warn(`属性 ${name} 未找到`);
            return;
        }
        
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.enableVertexAttribArray(location);
        gl.vertexAttribPointer(location, size, type, normalize, stride, offset);
    }
    
    /**
     * 设置uniform变量
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {WebGLProgram} program - 着色器程序
     * @param {string} name - uniform名
     * @param {any} value - 值
     * @param {string} type - 类型 ('1f', '2f', '3f', '4f', 'matrix4fv', etc.)
     */
    static setUniform(gl, program, name, value, type) {
        const location = gl.getUniformLocation(program, name);
        if (location === null) {
            console.warn(`Uniform ${name} 未找到`);
            return;
        }
        
        switch (type) {
            case '1f':
                gl.uniform1f(location, value);
                break;
            case '2f':
                gl.uniform2f(location, value[0], value[1]);
                break;
            case '3f':
                gl.uniform3f(location, value[0], value[1], value[2]);
                break;
            case '4f':
                gl.uniform4f(location, value[0], value[1], value[2], value[3]);
                break;
            case 'matrix4fv':
                gl.uniformMatrix4fv(location, false, value);
                break;
            default:
                console.warn(`未知的uniform类型: ${type}`);
        }
    }
    
    /**
     * 调整canvas大小以匹配显示大小
     * @param {HTMLCanvasElement} canvas - canvas元素
     * @returns {boolean} 是否调整了大小
     */
    static resizeCanvasToDisplaySize(canvas) {
        const displayWidth = canvas.clientWidth;
        const displayHeight = canvas.clientHeight;
        
        if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
            canvas.width = displayWidth;
            canvas.height = displayHeight;
            return true;
        }
        
        return false;
    }
    
    /**
     * 加载纹理
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {string} url - 图片URL
     * @returns {Promise<WebGLTexture>} 纹理对象
     */
    static loadTexture(gl, url) {
        return new Promise((resolve, reject) => {
            const texture = gl.createTexture();
            const image = new Image();
            
            image.onload = () => {
                gl.bindTexture(gl.TEXTURE_2D, texture);
                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, image);
                
                // 检查是否是2的幂次方
                if (this.isPowerOf2(image.width) && this.isPowerOf2(image.height)) {
                    gl.generateMipmap(gl.TEXTURE_2D);
                } else {
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
                }
                
                resolve(texture);
            };
            
            image.onerror = () => reject(new Error(`无法加载纹理: ${url}`));
            image.src = url;
        });
    }
    
    /**
     * 检查数字是否是2的幂次方
     * @param {number} value - 要检查的数字
     * @returns {boolean} 是否是2的幂次方
     */
    static isPowerOf2(value) {
        return (value & (value - 1)) === 0;
    }
    
    /**
     * 创建默认的1x1白色纹理
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @returns {WebGLTexture} 白色纹理
     */
    static createDefaultTexture(gl) {
        const texture = gl.createTexture();
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE, new Uint8Array([255, 255, 255, 255]));
        return texture;
    }
}

// 导出工具类
window.WebGLUtils = WebGLUtils;
