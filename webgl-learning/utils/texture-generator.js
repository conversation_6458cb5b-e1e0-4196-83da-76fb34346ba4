/**
 * 纹理生成器
 * 用于生成程序化纹理，避免依赖外部图片文件
 */

class TextureGenerator {
    /**
     * 创建棋盘格纹理
     * @param {number} size - 纹理大小
     * @param {number} squares - 格子数量
     * @param {Array} color1 - 颜色1 [r, g, b]
     * @param {Array} color2 - 颜色2 [r, g, b]
     * @returns {ImageData} 纹理数据
     */
    static createCheckerboard(size = 256, squares = 8, color1 = [255, 255, 255], color2 = [0, 0, 0]) {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const squareSize = size / squares;
        
        for (let y = 0; y < squares; y++) {
            for (let x = 0; x < squares; x++) {
                const isEven = (x + y) % 2 === 0;
                const color = isEven ? color1 : color2;
                
                ctx.fillStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
                ctx.fillRect(x * squareSize, y * squareSize, squareSize, squareSize);
            }
        }
        
        return ctx.getImageData(0, 0, size, size);
    }
    
    /**
     * 创建渐变纹理
     * @param {number} size - 纹理大小
     * @param {Array} color1 - 起始颜色 [r, g, b]
     * @param {Array} color2 - 结束颜色 [r, g, b]
     * @param {string} direction - 渐变方向 'horizontal' | 'vertical' | 'radial'
     * @returns {ImageData} 纹理数据
     */
    static createGradient(size = 256, color1 = [255, 0, 0], color2 = [0, 0, 255], direction = 'horizontal') {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        let gradient;
        
        switch (direction) {
            case 'horizontal':
                gradient = ctx.createLinearGradient(0, 0, size, 0);
                break;
            case 'vertical':
                gradient = ctx.createLinearGradient(0, 0, 0, size);
                break;
            case 'radial':
                gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
                break;
            default:
                gradient = ctx.createLinearGradient(0, 0, size, 0);
        }
        
        gradient.addColorStop(0, `rgb(${color1[0]}, ${color1[1]}, ${color1[2]})`);
        gradient.addColorStop(1, `rgb(${color2[0]}, ${color2[1]}, ${color2[2]})`);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, size, size);
        
        return ctx.getImageData(0, 0, size, size);
    }
    
    /**
     * 创建噪声纹理
     * @param {number} size - 纹理大小
     * @param {number} intensity - 噪声强度 (0-1)
     * @param {Array} baseColor - 基础颜色 [r, g, b]
     * @returns {ImageData} 纹理数据
     */
    static createNoise(size = 256, intensity = 0.5, baseColor = [128, 128, 128]) {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const imageData = ctx.createImageData(size, size);
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const noise = (Math.random() - 0.5) * intensity * 255;
            
            data[i] = Math.max(0, Math.min(255, baseColor[0] + noise));     // R
            data[i + 1] = Math.max(0, Math.min(255, baseColor[1] + noise)); // G
            data[i + 2] = Math.max(0, Math.min(255, baseColor[2] + noise)); // B
            data[i + 3] = 255; // A
        }
        
        return imageData;
    }
    
    /**
     * 创建圆形纹理
     * @param {number} size - 纹理大小
     * @param {Array} centerColor - 中心颜色 [r, g, b]
     * @param {Array} edgeColor - 边缘颜色 [r, g, b]
     * @returns {ImageData} 纹理数据
     */
    static createCircle(size = 256, centerColor = [255, 255, 255], edgeColor = [0, 0, 0]) {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
        gradient.addColorStop(0, `rgb(${centerColor[0]}, ${centerColor[1]}, ${centerColor[2]})`);
        gradient.addColorStop(1, `rgb(${edgeColor[0]}, ${edgeColor[1]}, ${edgeColor[2]})`);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, size, size);
        
        return ctx.getImageData(0, 0, size, size);
    }
    
    /**
     * 创建网格纹理
     * @param {number} size - 纹理大小
     * @param {number} gridSize - 网格大小
     * @param {Array} lineColor - 线条颜色 [r, g, b]
     * @param {Array} bgColor - 背景颜色 [r, g, b]
     * @param {number} lineWidth - 线条宽度
     * @returns {ImageData} 纹理数据
     */
    static createGrid(size = 256, gridSize = 32, lineColor = [0, 0, 0], bgColor = [255, 255, 255], lineWidth = 2) {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        // 填充背景
        ctx.fillStyle = `rgb(${bgColor[0]}, ${bgColor[1]}, ${bgColor[2]})`;
        ctx.fillRect(0, 0, size, size);
        
        // 绘制网格线
        ctx.strokeStyle = `rgb(${lineColor[0]}, ${lineColor[1]}, ${lineColor[2]})`;
        ctx.lineWidth = lineWidth;
        
        // 垂直线
        for (let x = 0; x <= size; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, size);
            ctx.stroke();
        }
        
        // 水平线
        for (let y = 0; y <= size; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(size, y);
            ctx.stroke();
        }
        
        return ctx.getImageData(0, 0, size, size);
    }
    
    /**
     * 从ImageData创建WebGL纹理
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {ImageData} imageData - 图像数据
     * @returns {WebGLTexture} WebGL纹理
     */
    static createWebGLTexture(gl, imageData) {
        const texture = gl.createTexture();
        gl.bindTexture(gl.TEXTURE_2D, texture);
        
        gl.texImage2D(
            gl.TEXTURE_2D,
            0,
            gl.RGBA,
            gl.RGBA,
            gl.UNSIGNED_BYTE,
            imageData
        );
        
        // 设置纹理参数
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
        
        return texture;
    }
}

// 导出纹理生成器
window.TextureGenerator = TextureGenerator;
