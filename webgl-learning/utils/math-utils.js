/**
 * 数学工具库
 * 提供3D图形编程所需的数学函数
 */

class MathUtils {
    /**
     * 角度转弧度
     * @param {number} degrees - 角度
     * @returns {number} 弧度
     */
    static degToRad(degrees) {
        return degrees * Math.PI / 180;
    }
    
    /**
     * 弧度转角度
     * @param {number} radians - 弧度
     * @returns {number} 角度
     */
    static radToDeg(radians) {
        return radians * 180 / Math.PI;
    }
    
    /**
     * 创建4x4单位矩阵
     * @returns {Float32Array} 单位矩阵
     */
    static identity() {
        return new Float32Array([
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ]);
    }
    
    /**
     * 创建平移矩阵
     * @param {number} x - X轴平移
     * @param {number} y - Y轴平移
     * @param {number} z - Z轴平移
     * @returns {Float32Array} 平移矩阵
     */
    static translation(x, y, z) {
        return new Float32Array([
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            x, y, z, 1
        ]);
    }
    
    /**
     * 创建绕X轴旋转矩阵
     * @param {number} angleInRadians - 旋转角度（弧度）
     * @returns {Float32Array} 旋转矩阵
     */
    static rotationX(angleInRadians) {
        const c = Math.cos(angleInRadians);
        const s = Math.sin(angleInRadians);
        
        return new Float32Array([
            1, 0, 0, 0,
            0, c, s, 0,
            0, -s, c, 0,
            0, 0, 0, 1
        ]);
    }
    
    /**
     * 创建绕Y轴旋转矩阵
     * @param {number} angleInRadians - 旋转角度（弧度）
     * @returns {Float32Array} 旋转矩阵
     */
    static rotationY(angleInRadians) {
        const c = Math.cos(angleInRadians);
        const s = Math.sin(angleInRadians);
        
        return new Float32Array([
            c, 0, -s, 0,
            0, 1, 0, 0,
            s, 0, c, 0,
            0, 0, 0, 1
        ]);
    }
    
    /**
     * 创建绕Z轴旋转矩阵
     * @param {number} angleInRadians - 旋转角度（弧度）
     * @returns {Float32Array} 旋转矩阵
     */
    static rotationZ(angleInRadians) {
        const c = Math.cos(angleInRadians);
        const s = Math.sin(angleInRadians);
        
        return new Float32Array([
            c, s, 0, 0,
            -s, c, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ]);
    }
    
    /**
     * 创建缩放矩阵
     * @param {number} x - X轴缩放
     * @param {number} y - Y轴缩放
     * @param {number} z - Z轴缩放
     * @returns {Float32Array} 缩放矩阵
     */
    static scaling(x, y, z) {
        return new Float32Array([
            x, 0, 0, 0,
            0, y, 0, 0,
            0, 0, z, 0,
            0, 0, 0, 1
        ]);
    }
    
    /**
     * 矩阵乘法
     * @param {Float32Array} a - 矩阵A
     * @param {Float32Array} b - 矩阵B
     * @returns {Float32Array} 结果矩阵
     */
    static multiply(a, b) {
        const result = new Float32Array(16);
        
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                result[i * 4 + j] = 
                    a[i * 4 + 0] * b[0 * 4 + j] +
                    a[i * 4 + 1] * b[1 * 4 + j] +
                    a[i * 4 + 2] * b[2 * 4 + j] +
                    a[i * 4 + 3] * b[3 * 4 + j];
            }
        }
        
        return result;
    }
    
    /**
     * 创建透视投影矩阵
     * @param {number} fieldOfViewInRadians - 视野角度（弧度）
     * @param {number} aspect - 宽高比
     * @param {number} near - 近裁剪面
     * @param {number} far - 远裁剪面
     * @returns {Float32Array} 透视投影矩阵
     */
    static perspective(fieldOfViewInRadians, aspect, near, far) {
        const f = Math.tan(Math.PI * 0.5 - 0.5 * fieldOfViewInRadians);
        const rangeInv = 1.0 / (near - far);
        
        return new Float32Array([
            f / aspect, 0, 0, 0,
            0, f, 0, 0,
            0, 0, (near + far) * rangeInv, -1,
            0, 0, near * far * rangeInv * 2, 0
        ]);
    }
    
    /**
     * 创建正交投影矩阵
     * @param {number} left - 左边界
     * @param {number} right - 右边界
     * @param {number} bottom - 下边界
     * @param {number} top - 上边界
     * @param {number} near - 近裁剪面
     * @param {number} far - 远裁剪面
     * @returns {Float32Array} 正交投影矩阵
     */
    static orthographic(left, right, bottom, top, near, far) {
        return new Float32Array([
            2 / (right - left), 0, 0, 0,
            0, 2 / (top - bottom), 0, 0,
            0, 0, 2 / (near - far), 0,
            (left + right) / (left - right),
            (bottom + top) / (bottom - top),
            (near + far) / (near - far),
            1
        ]);
    }
    
    /**
     * 创建lookAt矩阵（相机矩阵）
     * @param {Array} cameraPosition - 相机位置 [x, y, z]
     * @param {Array} target - 目标位置 [x, y, z]
     * @param {Array} up - 上方向 [x, y, z]
     * @returns {Float32Array} lookAt矩阵
     */
    static lookAt(cameraPosition, target, up) {
        const zAxis = this.normalize(this.subtractVectors(cameraPosition, target));
        const xAxis = this.normalize(this.cross(up, zAxis));
        const yAxis = this.normalize(this.cross(zAxis, xAxis));
        
        return new Float32Array([
            xAxis[0], xAxis[1], xAxis[2], 0,
            yAxis[0], yAxis[1], yAxis[2], 0,
            zAxis[0], zAxis[1], zAxis[2], 0,
            cameraPosition[0], cameraPosition[1], cameraPosition[2], 1
        ]);
    }
    
    /**
     * 向量减法
     * @param {Array} a - 向量A
     * @param {Array} b - 向量B
     * @returns {Array} 结果向量
     */
    static subtractVectors(a, b) {
        return [a[0] - b[0], a[1] - b[1], a[2] - b[2]];
    }
    
    /**
     * 向量叉积
     * @param {Array} a - 向量A
     * @param {Array} b - 向量B
     * @returns {Array} 叉积结果
     */
    static cross(a, b) {
        return [
            a[1] * b[2] - a[2] * b[1],
            a[2] * b[0] - a[0] * b[2],
            a[0] * b[1] - a[1] * b[0]
        ];
    }
    
    /**
     * 向量归一化
     * @param {Array} v - 向量
     * @returns {Array} 归一化后的向量
     */
    static normalize(v) {
        const length = Math.sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
        if (length > 0.00001) {
            return [v[0] / length, v[1] / length, v[2] / length];
        } else {
            return [0, 0, 0];
        }
    }
    
    /**
     * 矩阵求逆
     * @param {Float32Array} m - 输入矩阵
     * @returns {Float32Array} 逆矩阵
     */
    static inverse(m) {
        const inv = new Float32Array(16);
        
        inv[0] = m[5] * m[10] * m[15] - m[5] * m[11] * m[14] - m[9] * m[6] * m[15] + m[9] * m[7] * m[14] + m[13] * m[6] * m[11] - m[13] * m[7] * m[10];
        inv[4] = -m[4] * m[10] * m[15] + m[4] * m[11] * m[14] + m[8] * m[6] * m[15] - m[8] * m[7] * m[14] - m[12] * m[6] * m[11] + m[12] * m[7] * m[10];
        inv[8] = m[4] * m[9] * m[15] - m[4] * m[11] * m[13] - m[8] * m[5] * m[15] + m[8] * m[7] * m[13] + m[12] * m[5] * m[11] - m[12] * m[7] * m[9];
        inv[12] = -m[4] * m[9] * m[14] + m[4] * m[10] * m[13] + m[8] * m[5] * m[14] - m[8] * m[6] * m[13] - m[12] * m[5] * m[10] + m[12] * m[6] * m[9];
        inv[1] = -m[1] * m[10] * m[15] + m[1] * m[11] * m[14] + m[9] * m[2] * m[15] - m[9] * m[3] * m[14] - m[13] * m[2] * m[11] + m[13] * m[3] * m[10];
        inv[5] = m[0] * m[10] * m[15] - m[0] * m[11] * m[14] - m[8] * m[2] * m[15] + m[8] * m[3] * m[14] + m[12] * m[2] * m[11] - m[12] * m[3] * m[10];
        inv[9] = -m[0] * m[9] * m[15] + m[0] * m[11] * m[13] + m[8] * m[1] * m[15] - m[8] * m[3] * m[13] - m[12] * m[1] * m[11] + m[12] * m[3] * m[9];
        inv[13] = m[0] * m[9] * m[14] - m[0] * m[10] * m[13] - m[8] * m[1] * m[14] + m[8] * m[2] * m[13] + m[12] * m[1] * m[10] - m[12] * m[2] * m[9];
        inv[2] = m[1] * m[6] * m[15] - m[1] * m[7] * m[14] - m[5] * m[2] * m[15] + m[5] * m[3] * m[14] + m[13] * m[2] * m[7] - m[13] * m[3] * m[6];
        inv[6] = -m[0] * m[6] * m[15] + m[0] * m[7] * m[14] + m[4] * m[2] * m[15] - m[4] * m[3] * m[14] - m[12] * m[2] * m[7] + m[12] * m[3] * m[6];
        inv[10] = m[0] * m[5] * m[15] - m[0] * m[7] * m[13] - m[4] * m[1] * m[15] + m[4] * m[3] * m[13] + m[12] * m[1] * m[7] - m[12] * m[3] * m[5];
        inv[14] = -m[0] * m[5] * m[14] + m[0] * m[6] * m[13] + m[4] * m[1] * m[14] - m[4] * m[2] * m[13] - m[12] * m[1] * m[6] + m[12] * m[2] * m[5];
        inv[3] = -m[1] * m[6] * m[11] + m[1] * m[7] * m[10] + m[5] * m[2] * m[11] - m[5] * m[3] * m[10] - m[9] * m[2] * m[7] + m[9] * m[3] * m[6];
        inv[7] = m[0] * m[6] * m[11] - m[0] * m[7] * m[10] - m[4] * m[2] * m[11] + m[4] * m[3] * m[10] + m[8] * m[2] * m[7] - m[8] * m[3] * m[6];
        inv[11] = -m[0] * m[5] * m[11] + m[0] * m[7] * m[9] + m[4] * m[1] * m[11] - m[4] * m[3] * m[9] - m[8] * m[1] * m[7] + m[8] * m[3] * m[5];
        inv[15] = m[0] * m[5] * m[10] - m[0] * m[6] * m[9] - m[4] * m[1] * m[10] + m[4] * m[2] * m[9] + m[8] * m[1] * m[6] - m[8] * m[2] * m[5];
        
        let det = m[0] * inv[0] + m[1] * inv[4] + m[2] * inv[8] + m[3] * inv[12];
        
        if (det === 0) {
            return null;
        }
        
        det = 1.0 / det;
        
        for (let i = 0; i < 16; i++) {
            inv[i] = inv[i] * det;
        }
        
        return inv;
    }
}

// 导出数学工具类
window.MathUtils = MathUtils;
