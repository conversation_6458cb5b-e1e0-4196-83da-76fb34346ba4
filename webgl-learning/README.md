# 🎨 WebGL 学习之旅

一个精心设计的WebGL学习项目，从基础概念到高级技术，通过7个渐进式的交互demo帮助你掌握3D图形编程。

## 📚 学习路径

### 🔺 Demo 1: 基础三角形渲染
**学习目标**: WebGL基础概念
- 顶点着色器和片段着色器
- 缓冲区管理
- 渲染管线理解
- 基础的2D变换

**交互功能**:
- 实时调整三角形旋转和缩放
- 修改每个顶点的颜色
- 观察颜色插值效果

### 🔲 Demo 2: 矩形和基础变换
**学习目标**: 索引缓冲区和2D变换
- 索引缓冲区的使用
- 平移、旋转、缩放变换
- 矩阵运算基础
- 变换组合

**交互功能**:
- 独立控制X/Y轴的平移、缩放
- 旋转角度调节
- 实时重置变换
- 矩阵变换可视化

### 🧊 Demo 3: 3D立方体
**学习目标**: 进入3D世界
- 透视投影矩阵
- 深度测试和背面剔除
- 3D变换矩阵
- 相机系统基础

**交互功能**:
- 三轴独立旋转控制
- 相机距离和视野角度调节
- 自动旋转模式
- 线框/实体模式切换

### 🖼️ Demo 4: 纹理映射
**学习目标**: 纹理系统
- UV坐标系统
- 纹理加载和绑定
- 纹理过滤和包装
- 程序化纹理生成

**交互功能**:
- 多种纹理类型选择（棋盘格、渐变、噪声等）
- UV坐标缩放和偏移
- 纹理预览
- UV网格显示模式

### 💡 Demo 5: 光照系统
**学习目标**: 真实光照效果
- Phong光照模型
- 环境光、漫反射、镜面反射
- 法线向量处理
- 材质属性

**交互功能**:
- 环境光强度和颜色调节
- 方向光位置和颜色控制
- 材质光泽度调节
- 法线可视化模式

### 🎮 Demo 6: 交互式场景
**学习目标**: 用户交互和场景管理
- 鼠标和键盘事件处理
- 第一人称相机控制
- 射线检测和物体选择
- 动态场景管理

**交互功能**:
- WASD移动，鼠标视角控制
- 点击选择物体
- 动态添加/删除物体
- 相机参数调节

### ✨ Demo 7: 高级效果
**学习目标**: 高级渲染技术
- 粒子系统
- 帧缓冲和离屏渲染
- 后处理效果
- 性能优化技术

**交互功能**:
- 粒子数量、速度、大小控制
- 辉光、模糊、暗角后处理效果
- 实时效果切换
- 场景参数调节

## 🛠️ 技术特性

### 核心工具库
- **WebGL工具类**: 简化着色器创建、缓冲区管理
- **数学工具类**: 矩阵运算、向量操作
- **纹理生成器**: 程序化纹理创建

### 架构设计
- 模块化代码结构
- 可复用的工具函数
- 清晰的注释和文档
- 渐进式学习曲线

### 浏览器兼容性
- 支持所有现代浏览器
- WebGL 1.0标准
- 响应式设计
- 移动端友好

## 🚀 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd webgl-learning
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 或使用任何其他静态文件服务器
   ```

3. **打开浏览器**
   访问 `http://localhost:8000` 开始学习之旅

## 📖 学习建议

### 初学者路径
1. 按顺序完成每个demo
2. 仔细阅读代码注释
3. 尝试修改参数观察效果
4. 理解每个概念后再进入下一个

### 进阶学习
1. 研究着色器代码
2. 尝试添加新功能
3. 优化性能和代码结构
4. 实现自己的效果

### 调试技巧
1. 使用浏览器开发者工具
2. 检查WebGL错误信息
3. 逐步注释代码定位问题
4. 使用简单的测试用例

## 🎯 学习目标

完成所有demo后，你将掌握：

- **WebGL基础**: 着色器、缓冲区、渲染管线
- **3D数学**: 矩阵变换、投影、相机系统
- **图形技术**: 光照、纹理、后处理
- **交互设计**: 用户输入、场景管理
- **性能优化**: 批量渲染、帧缓冲

## 🔧 扩展建议

### 可以尝试添加的功能
- 阴影映射
- 法线贴图
- 环境映射
- 骨骼动画
- 物理模拟

### 性能优化方向
- 实例化渲染
- 视锥剔除
- LOD系统
- 纹理压缩
- 着色器优化

## 📝 注意事项

1. **浏览器要求**: 需要支持WebGL的现代浏览器
2. **性能考虑**: 某些demo可能在低端设备上运行较慢
3. **学习节奏**: 建议每天学习1-2个demo，充分理解后再继续
4. **实践为主**: 多动手修改代码，观察效果变化

## 🤝 贡献

欢迎提交问题报告、功能建议或代码改进！

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**开始你的WebGL学习之旅吧！** 🚀

记住：3D图形编程是一个需要耐心和实践的领域，不要急于求成，享受学习的过程！
